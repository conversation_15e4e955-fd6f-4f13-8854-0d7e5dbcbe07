<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>待预约申请单列表</span>
      </div>

      <!-- 搜索区域 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="患者姓名" prop="patientName">
          <el-input v-model="queryParams.patientName" placeholder="请输入患者姓名" clearable style="width: 240px"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="身份证号" prop="patientIdentity">
          <el-input v-model="queryParams.patientIdentity" placeholder="请输入身份证号" clearable style="width: 240px"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="申请时间">
          <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
            range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮区域 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-refresh" @click="handleSyncRequests"
            v-hasPermi="['reservation:studyRequest:sync']">同步申请单</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-magic-stick" @click="handleAutoReservation"
            v-hasPermi="['reservation:studyRequest:reservation']" :disabled="multiple">自动预约</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 申请单列表 -->
      <el-table v-loading="loading" :data="requestList" @selection-change="handleSelectionChange" border
        :max-height="500" style="width: 100%">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="申请单号" align="center" prop="requestId" :show-overflow-tooltip="true" />
        <el-table-column label="患者姓名" align="center" prop="patientName" width="100" />
        <el-table-column label="患者编号" align="center" prop="hisPatientId" width="120" :show-overflow-tooltip="true" />
        <el-table-column label="性别" align="center" prop="patientSex" width="60">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_user_sex" :value="scope.row.patientSex" />
          </template>
        </el-table-column>
        <el-table-column label="年龄" align="center" width="60">
          <template slot-scope="scope">
            {{ calculateAge(scope.row.patientBirthday) }}
          </template>
        </el-table-column>
        <el-table-column label="身份证号" align="center" prop="patientIdentity" width="180" :show-overflow-tooltip="true" />
        <el-table-column label="联系电话" align="center" prop="patientTelephone" width="120" />
        <el-table-column label="检查类别" align="center" prop="studyExamkind" width="100" />
        <el-table-column label="项目名称" align="center" prop="studyExamname" :show-overflow-tooltip="true" />
        <el-table-column label="检查部位" align="center" prop="studyBodypart" width="100" :show-overflow-tooltip="true" />
        <el-table-column label="申请科室" align="center" prop="studyApplydept" width="120" />
        <el-table-column label="执行科室" align="center" prop="studyPerformdept" width="120" />
        <el-table-column label="申请医生" align="center" prop="studyApplicant" width="100" />
        <el-table-column label="申请时间" align="center" prop="studyApplydttm" width="150">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.studyApplydttm) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" width="80">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.status === 1">已预约</el-tag>
            <el-tag type="info" v-else>未预约</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150" fixed="right">
          <template slot-scope="scope">
            <div class="operation-buttons">
              <!-- 未预约状态显示预约按钮 -->
              <el-button v-if="scope.row.status !== 1" size="mini" type="text" icon="el-icon-edit"
                @click="handleReservation(scope.row)"
                v-hasPermi="['reservation:studyRequest:reservation']">预约</el-button>
              <!-- 已预约状态显示取消预约和查看预约按钮 -->
              <el-button v-if="scope.row.status === 1" size="mini" type="text" icon="el-icon-close"
                @click="handleCancelAppointment(scope.row)"
                v-hasPermi="['reservation:studyRequest:reservation']">取消预约</el-button>
              <el-button v-if="scope.row.status === 1" size="mini" type="text" icon="el-icon-view"
                @click="handleViewAppointment(scope.row)"
                v-hasPermi="['reservation:studyRequest:query']">查看预约</el-button>
              <!-- 所有状态都显示详情按钮 -->
              <el-button size="mini" type="text" icon="el-icon-view" @click="handleDetail(scope.row)"
                v-hasPermi="['reservation:studyRequest:query']">详情</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </el-card>

    <!-- 申请单详情对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" label-width="100px">
        <!-- 患者基本信息 -->
        <el-divider content-position="left">患者基本信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="患者姓名" prop="patientName">
              <el-input v-model="form.patientName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="患者编号" prop="hisPatientId">
              <el-input v-model="form.hisPatientId" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别" prop="patientSex">
              <el-input v-model="form.patientSex" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="出生日期" prop="patientBirthday">
              <el-input v-model="form.patientBirthday" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号" prop="patientIdentity">
              <el-input v-model="form.patientIdentity" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话" prop="patientTelephone">
              <el-input v-model="form.patientTelephone" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="病人类型" prop="patientType">
              <el-input v-model="form.patientType" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="病人来源" prop="studySource">
              <el-input v-model="form.studySource" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="卡号" prop="patientCardno">
              <el-input v-model="form.patientCardno" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 申请信息 -->
        <el-divider content-position="left">申请信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="申请单号" prop="requestId">
              <el-input v-model="form.requestId" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查类别" prop="studyExamkind">
              <el-input v-model="form.studyExamkind" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目名称" prop="studyExamname">
              <el-input v-model="form.studyExamname" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="检查部位" prop="studyBodypart">
              <el-input v-model="form.studyBodypart" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查方法" prop="studyMethod">
              <el-input v-model="form.studyMethod" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请时间" prop="studyApplydttm">
              <el-input v-model="form.studyApplydttm" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="申请科室" prop="studyApplydept">
              <el-input v-model="form.studyApplydept" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请医生" prop="studyApplicant">
              <el-input v-model="form.studyApplicant" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="执行科室" prop="studyPerformdept">
              <el-input v-model="form.studyPerformdept" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="studyComment">
              <el-input type="textarea" v-model="form.studyComment" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 预约对话框 -->
    <el-dialog :title="'预约 - ' + reservationForm.patientName" :visible.sync="reservationOpen" width="65%" append-to-body
      :close-on-click-modal="false">
      <el-row :gutter="20" class="reservation-container">
        <!-- 左侧区域：执行科室和设备列表 -->
        <el-col :span="6" class="left-panel">
          <el-form ref="reservationForm" :model="reservationForm" :rules="rules" label-width="80px">
            <!-- 基本信息 -->
            <div class="patient-info">
              <div class="info-item">
                <span class="label">患者姓名：</span>
                <span class="value">{{ reservationForm.patientName }}</span>
              </div>
              <div class="info-item">
                <span class="label">患者编号：</span>
                <span class="value">{{ reservationForm.hisPatientId }}</span>
              </div>
              <div class="info-item" v-if="reservationForm.patientTelephone">
                <span class="label">手机号：</span>
                <span class="value">{{ reservationForm.patientTelephone }}</span>
              </div>
              <div class="info-item">
                <span class="label">检查项目：</span>
                <span class="value">{{ reservationForm.studyExamname }}</span>
              </div>
              <div class="info-item" v-if="reservationForm.studyPerformdept">
                <span class="label">执行科室：</span>
                <span class="value">{{ reservationForm.studyPerformdept }}</span>
              </div>
            </div>

            <!-- 预约日期选择 -->
            <el-form-item label="预约日期" prop="reservationDate">
              <el-date-picker v-model="reservationForm.reservationDate" type="date" placeholder="选择预约日期"
                style="width: 100%" :picker-options="datePickerOptions" @change="handleDateChange" />
            </el-form-item>

            <!-- 科室选择 -->
            <el-form-item label="选择科室" prop="deptId">
              <el-select v-model="reservationForm.deptId" placeholder="请选择科室" style="width: 100%"
                @change="handleDeptChange">
                <el-option v-for="item in deptOptions" :key="item.deptId" :label="item.deptName" :value="item.deptId" />
              </el-select>
            </el-form-item>

            <!-- 设备列表 -->
            <div class="device-list-container" v-if="deviceOptions.length > 0">
              <div class="section-title">设备列表</div>
              <ul class="device-list">
                <li v-for="device in deviceOptions" :key="device.deviceId"
                  :class="{ 'active': reservationForm.deviceId === device.deviceId }"
                  @click="selectDevice(device.deviceId)">
                  <i class="el-icon-monitor"></i>
                  <span>{{ device.deviceName }}</span>
                </li>
              </ul>
            </div>
            <div v-else class="no-devices">
              <i class="el-icon-info"></i>
              <span>请选择科室查看设备</span>
            </div>
          </el-form>
        </el-col>

        <!-- 右侧区域：日期和排班信息 -->
        <el-col :span="18" class="right-panel">
          <!-- 日期和星期显示 -->
          <div class="date-header" v-if="patientExams.length > 0">
            <!-- <div class="current-date">
              {{ formatDate(reservationForm.reservationDate) }}
              <span class="week-day">{{ getWeekDay(reservationForm.reservationDate) }}</span>
            </div> -->
            <div class="section-title">
              <div class="title-left">
                <span class="main-title">患者检查列表</span>
                <div class="exam-statistics" v-if="selectedExamsCount > 0">
                  <span class="stat-item">
                    <i class="el-icon-document"></i>
                    已选检查：{{ selectedExamsCount }} 项
                  </span>
                  <span class="stat-item">
                    <i class="el-icon-tickets"></i>
                    需占号数：{{ totalSelectedNumbers }} 个
                  </span>
                </div>
                <div class="exam-statistics" v-else>
                  <span class="stat-item-placeholder">
                    <i class="el-icon-info"></i>
                    请选择要预约的检查项目
                  </span>
                </div>
              </div>
              <div class="selection-controls">
                <el-checkbox v-model="selectAllChecked" @change="handleSelectAll"
                  :indeterminate="selectAllIndeterminate" style="margin-right: 15px;">
                  相同科室全选
                </el-checkbox>

              </div>
            </div>
            <div class="exams-scroll-container">
              <div class="exams-cards">
                <div v-if="patientExams.length === 0" class="no-exams">
                  <p>没有检查数据</p>
                </div>
                <div class="exam-card-wrapper" :class="{ 'status-reserved-wrapper': exam.status === 1 }"
                  @click="handleExamCardClick(exam)" v-for="(exam, index) in patientExams" :key="index">
                  <el-card shadow="hover" class="exam-card"
                    :class="{ 'checked-exam': exam.checked, 'status-reserved': exam.status === 1 }">
                    <div class="exam-checkbox">
                      <el-checkbox v-model="exam.checked" @click.stop.prevent :disabled="true">
                      </el-checkbox>
                    </div>
                    <div class="exam-title">
                      <div class="title-row">
                        <span class="dept-label">科室：</span>
                        <span class="dept-value">{{ exam.studyPerformdept }}</span>
                      </div>
                      <div class="title-row">
                        <span class="part-label">部位：</span>
                        <span class="part-value">{{ exam.studyExamname }}</span>
                      </div>
                    </div>

                    <div class="exam-info">
                      <p><i class="el-icon-location"></i> 检查部位：{{ exam.bodyInfo && exam.bodyInfo.bodyPartDictLabel ||
                        exam.studyBodypart || '未指定' }}，占号：{{ exam.bodyInfo && exam.bodyInfo.numberSource }} </p>
                    </div>
                    <div class="exam-status" :class="{ 'status-reserved': exam.status === 1 }">
                      {{ exam.status === 1 ? '已预约' : '未预约' }}
                    </div>
                  </el-card>
                </div>
              </div>
            </div>
          </div>

          <!-- 设备排班信息 -->
          <div class="schedule-container">
            <div v-if="!reservationForm.deviceId" class="no-device-selected">
              <i class="el-icon-warning"></i>
              <span>请在左侧选择设备查看排班信息</span>
            </div>
            <div v-else-if="!reservationForm.reservationDate" class="no-date-selected">
              <i class="el-icon-date"></i>
              <span>请选择预约日期</span>
            </div>
            <div v-else-if="availableTimeSlots.length === 0" class="no-schedules">
              <i class="el-icon-time"></i>
              <span>所选日期没有可用的排班计划</span>
            </div>
            <div v-else class="time-slots-wrapper">
              <div class="section-title">
                <div class="title-with-type">
                  <span>可预约时间段</span>
                  <div class="current-plan-type">
                    <span class="plan-type-label">当前选择：</span>
                    <el-tag size="small" type="success" v-if="reservationForm.planType === 0">普通号</el-tag>
                    <el-tag size="small" type="danger" v-if="reservationForm.planType === 1">急诊号</el-tag>
                    <el-tag size="small" type="primary" v-if="reservationForm.planType === 2">预留号</el-tag>
                  </div>
                </div>
              </div>
              <div class="time-slots">
                <el-card v-for="(slot, index) in availableTimeSlots" :key="index"
                  :class="{ 'selected-slot': slot.selected, 'disabled-slot': slot.disabled }" shadow="hover">
                  <div class="time-range">
                    <div class="time-display">{{ slot.startTime }} - {{ slot.endTime }}</div>
                    <div v-if="slot.disabled && slot.disabledReason" class="disabled-reason">
                      <el-tooltip :content="slot.disabledReasonFull || slot.disabledReason" placement="top"
                        :disabled="!slot.disabledReasonFull">
                        <span class="disabled-reason-text">{{ slot.disabledReason }}</span>
                      </el-tooltip>
                    </div>
                  </div>
                  <div class="remaining-counts">
                    <div class="remaining-count-item" @click.stop="!slot.disabled && selectPlanType(slot, 0)">
                      <el-tag type="success" size="small"
                        :class="{ 'selected-type': reservationForm.planType === 0 && slot.selected, 'disabled-tag': slot.disabled || slot.ordinaryRemaining <= 0 }">
                        普通号: <span class="count">{{ slot.ordinaryCount || 0 }}/{{ slot.ordinaryPlan }}</span>
                      </el-tag>
                    </div>
                    <div class="remaining-count-item" @click.stop="!slot.disabled && selectPlanType(slot, 1)">
                      <el-tag type="danger" size="small"
                        :class="{ 'selected-type': reservationForm.planType === 1 && slot.selected, 'disabled-tag': slot.disabled || slot.emergencyRemaining <= 0 }">
                        急诊号: <span class="count">{{ slot.emergencyCount || 0 }}/{{ slot.emergencyPlan }}</span>
                      </el-tag>
                    </div>
                    <div class="remaining-count-item" @click.stop="!slot.disabled && selectPlanType(slot, 2)"
                      v-if="slot.reservePlan != -1">
                      <el-tag type="primary" size="small"
                        :class="{ 'selected-type': reservationForm.planType === 2 && slot.selected, 'disabled-tag': slot.disabled || slot.reserveRemaining <= 0 }">
                        保留号: <span class="count">{{ slot.reserveCount || 0 }}/{{ slot.reservePlan }}</span>
                      </el-tag>
                    </div>
                    <div class="remaining-count-item total">
                      <el-tag size="small">
                        总计: <span class="count" v-if="slot.reservePlan == -1">{{ slot.totalCount || 0 }}/{{
                          slot.ordinaryPlan + slot.emergencyPlan }} </span>
                        <span class="count" v-else>{{ slot.totalCount || 0 }}/{{ slot.totalPlan }}</span>
                        <!-- 总计: <span class="count">{{ slot.totalCount || 0 }}/{{ slot.totalPlan }}</span> -->
                      </el-tag>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>


          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitReservation" :disabled="!reservationForm.timeSlot">确认预约</el-button>
        <el-button @click="cancelReservation">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStudyRequest, getStudyRequest, syncStudyRequests } from "@/api/reservation/studyRequest";
import { listDept } from "@/api/system/dept";
import { listModality } from "@/api/device/device";
import { listDevicePlan } from "@/api/device/plan";
import { cancelReservation, listReservation } from "@/api/reservation/reservation";
import { getAppointmentCount, autoSubmitAppointment, validateAppointment, batchSubmitAppointment } from "@/api/reservation/appointment";
import { parseTime } from "@/utils/ruoyi";

// 常量定义
const PLAN_TYPES = {
  ORDINARY: 0,    // 普通号
  EMERGENCY: 1,   // 急诊号
  RESERVED: 2     // 预留号
};

const APPOINTMENT_STATUS = {
  PENDING: 0,     // 未预约
  RESERVED: 1,    // 已预约
  EXAMINED: 2,    // 已检查
  CANCELLED: 3    // 已取消
};

const DATE_RANGE_DAYS = 7; // 日期选择范围天数

export default {
  name: "StudyRequestList",
  dicts: ['sys_user_sex', 'study_body_part_code'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 申请单表格数据
      requestList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示预约弹出层
      reservationOpen: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        patientName: undefined,
        patientIdentity: undefined,
        studyApplydttm: undefined,
        status: APPOINTMENT_STATUS.PENDING
      },
      // 表单参数
      form: {},
      // 预约表单
      reservationForm: this.getInitialReservationForm(),
      // 表单校验规则
      rules: this.getValidationRules(),
      // 科室选项
      deptOptions: [],
      // 设备选项
      deviceOptions: [],
      // 可用时间段
      availableTimeSlots: [],
      // 日期选择器配置
      datePickerOptions: this.getDatePickerOptions(),
      // 患者检查信息
      patientExams: [],
      // 多选相关数据
      selectAllChecked: false,
      selectAllIndeterminate: false,
      selectSameChecked: false
    };
  },
  computed: {
    // ==================== 计算属性 ====================

    /** 可用的检查项目 */
    availableExams() {
      return this.patientExams.filter(exam => !this.isReservedStatus(exam.status));
    },

    /** 已选择的检查项目 */
    selectedExams() {
      return this.availableExams.filter(exam => exam.checked);
    },

    /** 已选择检查项目数量 */
    selectedExamsCount() {
      return this.selectedExams.length;
    },

    /** 总需要的号源数量 */
    totalSelectedNumbers() {
      return this.selectedExams.reduce((total, exam) => {
        const numberSource = exam.bodyInfo?.numberSource;
        return total + (numberSource ? parseInt(numberSource, 10) || 1 : 1);
      }, 0);
    },

    /** 选中检查的部位统计 */
    selectedBodyPartCounts() {
      const counts = {};
      this.selectedExams.forEach(exam => {
        const bodyPart = exam.bodyInfo?.bodyPartDictLabel || exam.studyBodypart || '未知';
        counts[bodyPart] = (counts[bodyPart] || 0) + 1;
      });
      return counts;
    },

    /** 选中检查的部位统计文本 */
    selectedBodyPartCountsText() {
      const counts = this.selectedBodyPartCounts;
      return Object.entries(counts)
        .map(([bodyPart, count]) => `${bodyPart}(${count})`)
        .join('、');
    },

    /** 是否有选中的检查 */
    hasSelectedExams() {
      return this.selectedExamsCount > 0;
    },

    /** 当前选中的科室 */
    currentSelectedDept() {
      const deptId = this.reservationForm.deptId;
      return this.deptOptions.find(dept => dept.deptId === deptId);
    }
  },
  created() {
    this.initializeComponent();
  },
  methods: {
    // ==================== 初始化方法 ====================

    /** 组件初始化 */
    initializeComponent() {
      this.getList();
      this.getDeptList();
    },

    /** 获取初始预约表单 */
    getInitialReservationForm() {
      return {
        requestId: "",
        hisPatientId: "",
        patientName: "",
        studyExamname: "",
        reservationDate: "",
        deptId: "",
        deviceId: "",
        timeSlot: null,
        planType: PLAN_TYPES.ORDINARY
      };
    },

    /** 获取表单验证规则 */
    getValidationRules() {
      return {
        hisPatientId: [
          { required: true, message: "患者编号不能为空", trigger: "blur" }
        ],
        reservationDate: [
          { required: true, message: "请选择预约日期", trigger: "change" }
        ],
        deptId: [
          { required: true, message: "请选择预约科室", trigger: "change" }
        ],
        deviceId: [
          { required: true, message: "请选择预约设备", trigger: "change" }
        ]
      };
    },

    /** 获取日期选择器配置 */
    getDatePickerOptions() {
      return {
        disabledDate(time) {
          const today = new Date();
          today.setHours(0, 0, 0, 0);
          const limitDate = new Date(today);
          limitDate.setDate(today.getDate() + DATE_RANGE_DAYS);
          return time.getTime() < today.getTime() || time.getTime() >= limitDate.getTime();
        }
      };
    },

    // ==================== 数据获取方法 ====================

    /** 查询申请单列表 */
    async getList() {
      try {
        this.loading = true;
        const response = await listStudyRequest(this.addDateRange(this.queryParams, this.dateRange));
        this.requestList = response.rows;
        this.total = response.total;
      } catch (error) {
        console.error("获取申请单列表失败:", error);
        this.$modal.msgError("获取申请单列表失败");
      } finally {
        this.loading = false;
      }
    },

    /** 获取科室列表 */
    async getDeptList() {
      try {
        const response = await listDept();
        this.deptOptions = response.data;
        return this.deptOptions;
      } catch (error) {
        console.error("获取科室列表失败:", error);
        this.$modal.msgError("获取科室列表失败");
        return [];
      }
    },

    /** 获取设备列表 */
    async getDeviceList(deptId) {
      try {
        this.loading = true;
        const response = await listModality({ deptId: deptId });
        this.deviceOptions = response.rows || [];

        // 如果有设备，默认选中第一个
        if (this.deviceOptions.length > 0) {
          this.reservationForm.deviceId = this.deviceOptions[0].deviceId;
          this.handleDeviceChange();
        }

        return this.deviceOptions;
      } catch (error) {
        console.error("获取设备列表失败:", error);
        this.$modal.msgError("获取设备列表失败");
        return [];
      } finally {
        this.loading = false;
      }
    },

    // ==================== 工具方法 ====================

    /** 计算年龄 */
    calculateAge(birthday) {
      if (!birthday) return "";

      const birthDate = new Date(birthday);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const monthDiff = today.getMonth() - birthDate.getMonth();

      if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }

      return age + "岁";
    },

    /** 获取预约类型标签 */
    getPlanTypeLabel(planType) {
      const labels = {
        [PLAN_TYPES.ORDINARY]: '普通号',
        [PLAN_TYPES.EMERGENCY]: '急诊号',
        [PLAN_TYPES.RESERVED]: '预留号'
      };
      return labels[planType] || '未知';
    },

    /** 检查是否为已预约状态 */
    isReservedStatus(status) {
      return status === APPOINTMENT_STATUS.RESERVED;
    },

    // ==================== 表单处理方法 ====================

    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },

    /** 表单重置 */
    reset() {
      this.form = {};
      this.resetForm("form");
    },

    /** 重置预约表单 */
    resetReservationForm(partial = false) {
      if (partial) {
        // 只重置检查相关信息，保留时间和设备设置
        Object.assign(this.reservationForm, {
          requestId: "",
          studyExamname: "",
          studyBodypart: "",
          studyBodypartCode: "",
          bodyInfo: null,
          numberSource: ""
        });
      } else {
        // 完全重置
        this.reservationForm = this.getInitialReservationForm();
        this.availableTimeSlots = [];
        this.patientExams = [];
        this.resetSelectionState();
      }
      this.loading = false;
    },

    /** 重置选择状态 */
    resetSelectionState() {
      this.selectAllChecked = false;
      this.selectAllIndeterminate = false;
      this.selectSameChecked = false;
    },

    // ==================== 事件处理方法 ====================

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.requestId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    // ==================== 业务操作方法 ====================

    /** 同步申请单 */
    async handleSyncRequests() {
      try {
        await this.$modal.confirm('确认同步外部系统的申请单数据吗？');
        this.loading = true;
        const response = await syncStudyRequests();
        this.$modal.msgSuccess(`同步成功，共同步 ${response.data} 条数据`);
        await this.getList();
      } catch (error) {
        if (error !== 'cancel') {
          console.error("同步申请单失败:", error);
          this.$modal.msgError("同步失败，请稍后重试");
        }
      } finally {
        this.loading = false;
      }
    },

    /** 自动预约按钮操作 */
    async handleAutoReservation() {
      // 验证选择
      const validationResult = this.validateAutoReservationSelection();
      if (!validationResult.isValid) {
        this.$modal.msgWarning(validationResult.message);
        return;
      }

      try {
        await this.$modal.confirm(`确认对选中的 ${this.ids.length} 个申请单进行自动预约吗？`);
        this.loading = true;
        const response = await autoSubmitAppointment(this.ids);

        if (response.code === 200) {
          this.$modal.msgSuccess(`自动预约成功，共预约 ${response.data} 个申请单`);
          await this.getList();
        } else {
          this.$modal.msgError(`自动预约失败：${response.msg}`);
        }
      } catch (error) {
        if (error !== 'cancel') {
          console.error("自动预约失败:", error);
          this.$modal.msgError("自动预约失败，请稍后重试");
        }
      } finally {
        this.loading = false;
      }
    },

    /** 验证自动预约选择 */
    validateAutoReservationSelection() {
      if (this.ids.length === 0) {
        return { isValid: false, message: "请至少选择一个申请单进行自动预约" };
      }

      const selectedRows = this.requestList.filter(row => this.ids.includes(row.requestId));
      const alreadyReserved = selectedRows.filter(row => this.isReservedStatus(row.status));

      if (alreadyReserved.length > 0) {
        return {
          isValid: false,
          message: `您选择的申请单中有 ${alreadyReserved.length} 个已经预约，请重新选择`
        };
      }

      return { isValid: true };
    },
    /** 详情按钮操作 */
    async handleDetail(row) {
      try {
        this.reset();
        const requestId = row.requestId || this.ids;
        const response = await getStudyRequest(requestId);
        this.form = response.data;

        // 格式化日期
        if (this.form.studyApplydttm) {
          this.form.studyApplydttm = parseTime(this.form.studyApplydttm);
        }

        this.open = true;
        this.title = "申请单详情";
      } catch (error) {
        console.error("获取申请单详情失败:", error);
        this.$modal.msgError("获取申请单详情失败");
      }
    },

    /** 预约按钮操作 */
    async handleReservation(row) {
      // 验证预约条件
      const validationResult = this.validateReservationConditions(row);
      if (!validationResult.isValid) {
        this.$modal.msgWarning(validationResult.message);
        return;
      }

      try {
        // 初始化预约表单
        this.initializeReservationForm(row);

        // 加载科室列表并设置默认科室
        await this.getDeptList();
        this.setDefaultDeptForReservation(row);

        // 加载患者检查数据
        await this.loadPatientExams(row.hisPatientId);

        // 打开预约对话框
        this.reservationOpen = true;
      } catch (error) {
        console.error("初始化预约失败:", error);
        this.$modal.msgError("初始化预约失败，请稍后重试");
      }
    },

    /** 验证预约条件 */
    validateReservationConditions(row) {
      if (this.isReservedStatus(row.status)) {
        return { isValid: false, message: "该申请单已经预约，无法再次预约" };
      }

      if (!row.hisPatientId) {
        return { isValid: false, message: "患者编号不存在，无法进行预约" };
      }

      return { isValid: true };
    },

    /** 初始化预约表单 */
    initializeReservationForm(row) {
      this.reservationForm = {
        ...this.getInitialReservationForm(),
        requestId: row.requestId,
        hisPatientId: row.hisPatientId,
        patientName: row.patientName,
        patientTelephone: row.patientTelephone,
        patientSex: row.patientSex,
        studyExamname: row.studyExamname,
        studyPerformdept: row.studyPerformdept,
        reservationDate: parseTime(new Date(), '{y}-{m}-{d}')
      };

      this.availableTimeSlots = [];
      this.deviceOptions = [];
    },

    /** 为预约设置默认科室 */
    setDefaultDeptForReservation(row) {
      if (row.studyPerformdeptid) {
        this.setDefaultDept(row.studyPerformdeptid);
      } else if (row.studyPerformdept) {
        const matchedDept = this.deptOptions.find(dept => dept.deptName === row.studyPerformdept);
        if (matchedDept) {
          this.setDefaultDept(matchedDept.deptId);
        }
      }
    },

    /** 加载患者的所有检查申请单 */
    async loadPatientExams(patientId) {
      if (!patientId) {
        this.patientExams = [];
        return;
      }

      try {
        this.loading = true;
        const response = await listStudyRequest({
          hisPatientId: patientId,
          status: APPOINTMENT_STATUS.PENDING
        });

        this.patientExams = response.rows || [];
        this.processPatientExamsData();
        this.resetSelectionState();
        this.autoSelectSameDeptExams();
      } catch (error) {
        console.error("加载患者检查数据失败:", error);
        this.patientExams = [];
        this.$modal.msgError("加载患者检查数据失败");
      } finally {
        this.loading = false;
      }
    },

    /** 处理患者检查数据 */
    processPatientExamsData() {
      const bodyPartDict = this.dict.type.study_body_part_code;

      this.patientExams.forEach(exam => {
        this.$set(exam, 'checked', false);

        if (bodyPartDict && exam.studyBodypartCode) {
          const matchedDict = bodyPartDict.find(dict => dict.value === exam.studyBodypartCode);
          if (matchedDict) {
            exam.bodyInfo = {
              bodyPartDictLabel: matchedDict.label,
              bodyPartDictValue: matchedDict.value,
              numberSource: matchedDict.raw.standby1
            };
          }
        }
      });
    },

    /** 自动勾选与当前选择科室相同的检查项目 */
    autoSelectSameDeptExams() {
      const currentDeptId = this.reservationForm.deptId;
      if (!currentDeptId) return;

      const selectedDept = this.deptOptions.find(dept => dept.deptId === currentDeptId);
      if (!selectedDept) return;

      const currentDeptName = selectedDept.deptName;

      // 自动勾选与当前科室相同的检查项目
      this.patientExams.forEach(exam => {
        if (!this.isReservedStatus(exam.status) && exam.studyPerformdept === currentDeptName) {
          this.$set(exam, 'checked', true);
        }
      });

      this.updateSelectAllState();
    },



    // ==================== 检查选择相关方法 ====================

    /** 检查是否可以选择该检查 */
    canSelectExam(exam, newState) {
      if (this.isReservedStatus(exam.status)) {
        this.$message.warning('该检查已预约，无法操作');
        return false;
      }

      if (!newState) return true; // 取消选择总是允许的

      const currentDept = exam.studyPerformdept || '';
      const otherDeptSelected = this.selectedExams.find(selectedExam =>
        selectedExam.requestId !== exam.requestId &&
        selectedExam.studyPerformdept !== currentDept
      );

      if (otherDeptSelected) {
        this.$message.warning('只能选择相同科室的检查项目');
        return false;
      }

      return true;
    },

    /** 处理检查卡片点击 */
    async handleExamCardClick(exam) {
      try {
        this.$modal.loading('加载中...');

        const newCheckedState = !exam.checked;

        // 检查是否可以选择该检查
        if (!this.canSelectExam(exam, newCheckedState)) {
          return;
        }

        // 设置新的选中状态
        this.$set(exam, 'checked', newCheckedState);

        // 清空已选择的时间段
        this.clearSelectedTimeSlot();
        console.log("点击了检查卡片");
        if (newCheckedState) {
          this.selectExam(exam);
          await this.getAvailableTimeSlots();
        } else {
          this.handleDateChange();
        }

        this.updateSelectAllState();
      } finally {
        this.$modal.closeLoading();
      }
    },

    /** 清空选中的时间段 */
    clearSelectedTimeSlot() {
      this.reservationForm.timeSlot = null;
      this.reservationForm.planType = PLAN_TYPES.ORDINARY;
    },

    /** 更新全选状态 */
    updateSelectAllState() {
      const selectedCount = this.selectedExamsCount;
      const availableCount = this.availableExams.length;

      if (selectedCount === 0) {
        this.selectAllChecked = false;
        this.selectAllIndeterminate = false;
      } else if (selectedCount === availableCount) {
        this.selectAllChecked = true;
        this.selectAllIndeterminate = false;
      } else {
        this.selectAllChecked = false;
        this.selectAllIndeterminate = true;
      }
    },

    /** 处理全选 */
    handleSelectAll(checked) {
      if (checked) {
        const targetDept = this.getCurrentSelectedDept();
        if (!targetDept) return;

        // 只选择目标科室的检查
        this.availableExams.forEach(exam => {
          this.$set(exam, 'checked', exam.studyPerformdept === targetDept.deptName);
        });
      } else {
        // 取消所有选择
        this.availableExams.forEach(exam => {
          this.$set(exam, 'checked', false);
        });
      }

      this.selectAllIndeterminate = false;
    },

    /** 处理相同全选 */
    handleSelectSame(checked) {
      const validationResult = this.validateSelectSameOperation();
      if (!validationResult.isValid) {
        this.$modal.msgWarning(validationResult.message);
        this.selectSameChecked = false;
        return;
      }

      const currentDept = this.selectedExams[0].studyPerformdept;

      // 选择相同科室的所有检查
      this.availableExams.forEach(exam => {
        if (exam.studyPerformdept === currentDept) {
          this.$set(exam, 'checked', checked);
        }
      });

      this.updateSelectAllState();
    },

    /** 获取当前选中的科室 */
    getCurrentSelectedDept() {
      const currentDeptId = this.reservationForm.deptId;
      if (!currentDeptId) return null;

      return this.deptOptions.find(dept => dept.deptId === currentDeptId);
    },

    /** 验证相同选择操作 */
    validateSelectSameOperation() {
      if (this.selectedExams.length === 0) {
        return { isValid: false, message: '请先勾选一个检查项目' };
      }

      const currentDept = this.selectedExams[0].studyPerformdept;
      if (!currentDept) {
        return { isValid: false, message: '选中的检查项目没有科室信息' };
      }

      return { isValid: true };
    },


    // ==================== 科室和设备管理方法 ====================

    /** 设置默认科室 */
    setDefaultDept(deptId, resetDeviceAndTime = true) {
      const deptIdNum = parseInt(deptId, 10);
      const matchedDept = this.deptOptions.find(dept => dept.deptId === deptIdNum);

      this.reservationForm.deptId = matchedDept ? matchedDept.deptId : deptIdNum;
      this.reservationForm.studyPerformdept = matchedDept?.deptName;

      // 根据参数决定是否重置设备和时间
      if (resetDeviceAndTime) {
        this.handleDeptChange(this.reservationForm.deptId);
      } else {
        this.handleDeptChangeWithoutReset(this.reservationForm.deptId);
      }
    },

    /** 科室变更事件 */
    handleDeptChange(deptId) {
      // 清空设备选择和时间段
      this.reservationForm.deviceId = "";
      this.clearSelectedTimeSlot();
      this.availableTimeSlots = [];

      // 获取该科室下的设备列表
      if (deptId) {
        this.getDeviceList(deptId);
      } else {
        this.deviceOptions = [];
      }
    },

    /** 科室变更事件但不重置已选设备和时间段 */
    async handleDeptChangeWithoutReset(deptId) {
      if (deptId) {
        await this.getDeviceList(deptId);
        this.handleDeviceChange();
      } else {
        this.deviceOptions = [];
      }
    },
    /** 日期变更事件 */
    handleDateChange() {
      this.clearSelectedTimeSlot();

      // 如果已选择设备，则重新获取可用时间段
      if (this.reservationForm.deviceId) {
        this.getAvailableTimeSlots();
      }
    },

    /** 设备变更事件 */
    handleDeviceChange() {
      this.clearSelectedTimeSlot();

      if (this.reservationForm.reservationDate) {
        this.getAvailableTimeSlots();
      } else {
        this.availableTimeSlots = [];
      }
    },

    /** 选择设备 */
    selectDevice(deviceId) {
      this.reservationForm.deviceId = deviceId;
      this.handleDeviceChange();
    },
    // ==================== 时间段管理方法 ====================

    /** 创建时间段对象 */
    createTimeSlot(plan) {
      const timeSlot = {
        startTime: plan.stime,
        endTime: plan.etime,
        planId: plan.planId,
        disabled: false,
        selected: false,
        ordinaryPlan: plan.ordinaryPlan,
        emergencyPlan: plan.emergencyPlan,
        reservePlan: plan.reservePlan,
        totalPlan: plan.totalPlan,
        ordinaryCount: 0,
        emergencyCount: 0,
        reserveCount: 0,
        totalCount: 0
      };

      // 检查是否已过期
      if (this.isTimeSlotExpired(plan)) {
        timeSlot.disabled = true;
        timeSlot.disabledReason = '已过期';
      }

      return timeSlot;
    },

    /** 检查时间段是否已过期 */
    isTimeSlotExpired(plan) {
      const now = new Date();
      const planDate = new Date(parseTime(this.reservationForm.reservationDate, '{y}-{m}-{d}'));
      const [hour, minute] = plan.etime.split(':').map(Number);
      planDate.setHours(hour, minute, 0, 0);
      return planDate < now;
    },

    /** 更新时间段预约数量 */
    updateTimeSlotCounts(timeSlot, countData) {
      // 更新各类型预约数量
      countData.forEach(item => {
        if (item.planId === timeSlot.planId) {
          const count = item.appointmentCount || 0;
          switch (item.planType) {
            case PLAN_TYPES.ORDINARY:
              timeSlot.ordinaryCount = count;
              break;
            case PLAN_TYPES.EMERGENCY:
              timeSlot.emergencyCount = count;
              break;
            case PLAN_TYPES.RESERVED:
              timeSlot.reserveCount = count;
              break;
          }
        }
      });

      // 计算剩余数量
      this.calculateTimeSlotRemaining(timeSlot);
    },

    /** 计算时间段剩余数量 */
    calculateTimeSlotRemaining(timeSlot) {
      timeSlot.totalCount = timeSlot.ordinaryCount + timeSlot.emergencyCount + timeSlot.reserveCount;
      timeSlot.ordinaryRemaining = Math.max(0, timeSlot.ordinaryPlan - timeSlot.ordinaryCount);
      timeSlot.emergencyRemaining = Math.max(0, timeSlot.emergencyPlan - timeSlot.emergencyCount);
      timeSlot.reserveRemaining = Math.max(0, timeSlot.reservePlan - timeSlot.reserveCount);
      timeSlot.remainingCount = Math.max(0, timeSlot.totalPlan - timeSlot.totalCount);

      // 检查是否已约满
      if (timeSlot.remainingCount <= 0 && !timeSlot.disabledReason) {
        timeSlot.disabled = true;
        timeSlot.disabledReason = '已约满';
      }
    },

    /** 获取可用时间段 */
    async getAvailableTimeSlots() {
      if (!this.validateTimeSlotParams()) {
        return;
      }

      try {
        this.loading = true;

        // 获取设备计划列表
        const plans = await this.fetchDevicePlans();

        // 创建时间段对象
        this.availableTimeSlots = plans.map(plan => this.createTimeSlot(plan));

        // 获取预约数量并更新时间段
        await this.updateTimeSlotsWithCounts();

        // 如果有选中的检查，进行验证和自动选择
        if (this.selectedExams.length > 0) {
          await this.validateTimeSlots();
          this.autoSelectAvailableOrdinarySlot();
        }
      } catch (error) {
        console.error('获取时间段失败:', error);
        this.$modal.msgError('获取时间段失败，请稍后重试');
      } finally {
        this.loading = false;
      }
    },

    /** 验证时间段参数 */
    validateTimeSlotParams() {
      return this.reservationForm.deviceId && this.reservationForm.reservationDate;
    },

    /** 获取设备计划 */
    async fetchDevicePlans() {
      const params = {
        deviceId: this.reservationForm.deviceId,
        planDate: parseTime(this.reservationForm.reservationDate, '{y}-{m}-{d}')
      };

      const response = await listDevicePlan(params);
      return response.rows || [];
    },

    /** 更新时间段预约数量 */
    async updateTimeSlotsWithCounts() {
      const countPromises = this.availableTimeSlots.map(timeSlot =>
        this.fetchTimeSlotCount(timeSlot)
      );

      await Promise.all(countPromises);
    },

    /** 获取单个时间段的预约数量 */
    async fetchTimeSlotCount(timeSlot) {
      try {
        const countParams = {
          deviceId: this.reservationForm.deviceId,
          planId: timeSlot.planId,
          appointmentDate: parseTime(this.reservationForm.reservationDate, '{y}-{m}-{d}'),
          _t: new Date().getTime()
        };

        const countResponse = await getAppointmentCount(countParams);

        if (countResponse.code === 200 && countResponse.data) {
          this.updateTimeSlotCounts(timeSlot, countResponse.data);
        }
      } catch (error) {
        // 使用默认值
        this.setDefaultTimeSlotCounts(timeSlot);
      }
    },

    /** 设置时间段默认数量 */
    setDefaultTimeSlotCounts(timeSlot) {
      timeSlot.ordinaryRemaining = timeSlot.ordinaryPlan;
      timeSlot.emergencyRemaining = timeSlot.emergencyPlan;
      timeSlot.reserveRemaining = timeSlot.reservePlan;
      timeSlot.remainingCount = timeSlot.totalPlan;
    },

    // ==================== 时间段验证方法 ====================

    /** 验证时间段是否可以预约 */
    async validateTimeSlots() {
      conso
      if (!this.availableTimeSlots.length || !this.selectedExams.length) {
        return;
      }

      try {
        const validationPromises = this.createValidationPromises();
        const validationResults = await Promise.all(validationPromises);
        this.processValidationResults(validationResults);
      } catch (error) {
        console.error('批量验证时间段失败:', error);
      }
    },

    /** 创建验证Promise数组 */
    createValidationPromises() {
      const validationPromises = [];

      for (const exam of this.selectedExams) {
        for (const slot of this.availableTimeSlots) {
          if (slot.disabled) continue;

          const validationPromise = this.validateSingleSlot(exam.requestId, slot.planId, PLAN_TYPES.ORDINARY)
            .then(result => ({
              requestId: exam.requestId,
              planId: slot.planId,
              isValid: result.isValid,
              reason: result.reason
            }))
            .catch(error => {
              console.error(`验证失败 - 申请单: ${exam.requestId}, 时间段: ${slot.planId}`, error);
              return {
                requestId: exam.requestId,
                planId: slot.planId,
                isValid: false,
                reason: '验证接口调用失败'
              };
            });

          validationPromises.push(validationPromise);
        }
      }

      return validationPromises;
    },

    /** 验证单个时间段 */
    async validateSingleSlot(requestId, planId, planType) {
      try {
        const response = await validateAppointment({
          requestId,
          planId,
          planType
        });

        // 通过 msg 字段判断：msg 为空表示可预约，有内容表示不可预约
        const isValid = !response.msg || response.msg.trim() === '';

        return {
          isValid,
          reason: response.msg || ''
        };
      } catch (error) {
        return {
          isValid: false,
          reason: error.message || '验证接口调用失败'
        };
      }
    },

    /** 处理验证结果 */
    processValidationResults(validationResults) {
      const slotValidations = this.groupValidationsBySlot(validationResults);
      this.updateSlotsValidationStatus(slotValidations);
    },

    /** 按时间段分组验证结果 */
    groupValidationsBySlot(validationResults) {
      const slotValidations = {};

      validationResults.forEach(result => {
        const key = result.planId;
        if (!slotValidations[key]) {
          slotValidations[key] = [];
        }
        slotValidations[key].push(result);
      });

      return slotValidations;
    },

    /** 更新时间段验证状态 */
    updateSlotsValidationStatus(slotValidations) {
      this.availableTimeSlots.forEach(slot => {
        const validations = slotValidations[slot.planId] || [];
        const hasInvalidExam = validations.some(v => !v.isValid);

        if (hasInvalidExam) {
          const invalidReasons = validations.filter(v => !v.isValid).map(v => v.reason);
          const fullReason = invalidReasons[0] || '验证失败';

          slot.disabled = true;
          slot.disabledReasonFull = fullReason;
          slot.disabledReason = fullReason;
        }
      });
    },



    // ==================== 时间段选择方法 ====================

    /** 自动选择普通号可用的时间段 */
    autoSelectAvailableOrdinarySlot() {
      if (this.reservationForm.timeSlot) return;

      const requiredNumbers = this.totalSelectedNumbers || 1;
      const availableSlots = this.getAvailableOrdinarySlots(requiredNumbers);

      if (availableSlots.length > 0) {
        const firstAvailableSlot = availableSlots[0];
        this.reservationForm.planType = PLAN_TYPES.ORDINARY;
        this.selectPlanType(firstAvailableSlot, PLAN_TYPES.ORDINARY);
      }
    },

    /** 获取可用的普通号时间段 */
    getAvailableOrdinarySlots(requiredNumbers) {
      return this.availableTimeSlots.filter(slot =>
        !slot.disabled && slot.ordinaryRemaining >= requiredNumbers
      );
    },

    /** 选择可约时间段 */
    selectTimeSlot(slot) {
      if (slot.disabled) return;

      // 验证号源是否充足
      if (!this.validateSlotAvailability(slot)) {
        return;
      }

      // 更新时间段选择状态
      this.updateTimeSlotSelection(slot);

      // 保存时间段到表单和患者记录
      this.saveTimeSlotSelection(slot);
    },

    /** 验证时间段可用性 */
    validateSlotAvailability(slot) {
      const type = this.reservationForm.planType;
      const typeLabels = {
        [PLAN_TYPES.ORDINARY]: '普通号',
        [PLAN_TYPES.EMERGENCY]: '急诊号',
        [PLAN_TYPES.RESERVED]: '保留号'
      };

      const remainingCounts = {
        [PLAN_TYPES.ORDINARY]: slot.ordinaryRemaining,
        [PLAN_TYPES.EMERGENCY]: slot.emergencyRemaining,
        [PLAN_TYPES.RESERVED]: slot.reserveRemaining
      };

      if (remainingCounts[type] <= 0) {
        this.$modal.msgWarning(`${typeLabels[type]}已经预约满了，请选择其他类型的号`);
        return false;
      }

      return true;
    },

    /** 更新时间段选择状态 */
    updateTimeSlotSelection(slot) {
      // 取消之前选择的时间段
      this.availableTimeSlots.forEach(item => {
        item.selected = false;
      });

      // 选中当前时间段
      slot.selected = true;

      // 确保时间段对象中包含预约类型信息
      if (slot.planType === undefined && this.reservationForm.planType !== undefined) {
        slot.planType = this.reservationForm.planType;
      }
    },

    /** 保存时间段选择 */
    saveTimeSlotSelection(slot) {
      this.reservationForm.timeSlot = slot;

      // 保存到患者检查记录
      if (this.reservationForm.requestId) {
        const patient = this.patientExams.find(item => item.requestId === this.reservationForm.requestId);
        if (patient) {
          this.savePatientTimeSlot(patient, slot);
        }
      }
    },

    /** 保存患者时间段信息 */
    savePatientTimeSlot(patient, slot) {
      const timeSlotCopy = JSON.parse(JSON.stringify(slot));

      if (this.reservationForm.planType !== undefined) {
        timeSlotCopy.planType = this.reservationForm.planType;
        patient.selectedPlanType = this.reservationForm.planType;
      }

      patient.timeSlot = timeSlotCopy;
    },

    /** 选择预约类型 */
    selectPlanType(slot, type) {
      if (slot.disabled) return;

      // 验证号源是否充足
      if (!this.validatePlanTypeAvailability(slot, type)) {
        return;
      }

      // 更新预约类型
      this.updatePlanTypeSelection(type);

      // 如果时间段还没有被选中，则选中它
      if (!slot.selected) {
        this.selectTimeSlot(slot);
      }
    },

    /** 验证预约类型可用性 */
    validatePlanTypeAvailability(slot, type) {
      const requiredCount = this.totalSelectedNumbers;
      const typeLabels = {
        [PLAN_TYPES.ORDINARY]: '普通号',
        [PLAN_TYPES.EMERGENCY]: '急诊号',
        [PLAN_TYPES.RESERVED]: '保留号'
      };

      const remainingCounts = {
        [PLAN_TYPES.ORDINARY]: slot.ordinaryRemaining,
        [PLAN_TYPES.EMERGENCY]: slot.emergencyRemaining,
        [PLAN_TYPES.RESERVED]: slot.reserveRemaining
      };

      if (remainingCounts[type] - requiredCount < 0) {
        this.$modal.msgWarning(`${typeLabels[type]}余量不足，请选择其他类型的号`);
        return false;
      }

      return true;
    },

    /** 更新预约类型选择 */
    updatePlanTypeSelection(type) {
      // 设置预约类型
      this.reservationForm.planType = type;

      // 更新患者记录中的预约类型
      const patient = this.patientExams.find(item => item.requestId === this.reservationForm.requestId);
      if (patient) {
        patient.selectedPlanType = type;
        if (patient.timeSlot) {
          patient.timeSlot.planType = type;
        }
      }
    },

    // ==================== 格式化和工具方法 ====================

    /** 格式化日期显示 */
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}年${month}月${day}日`;
    },

    /** 获取星期几 */
    getWeekDay(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];
      return weekDays[date.getDay()];
    },

    /** 获取选中的设备名称 */
    getSelectedDeviceName() {
      if (!this.reservationForm.deviceId) return '';
      const device = this.deviceOptions.find(item => item.deviceId === this.reservationForm.deviceId);
      return device ? device.deviceName : '';
    },

    // ==================== 预约提交相关方法 ====================
    /** 提交预约 */
    async submitReservation() {
      try {
        // 表单验证
        const isValid = await this.validateReservationForm();
        if (!isValid) return;

        // 业务验证
        const businessValidation = this.validateReservationBusiness();
        if (!businessValidation.isValid) {
          this.$message.warning(businessValidation.message);
          return;
        }

        // 提交预约
        await this.performReservationSubmit();
      } catch (error) {
        console.error("预约提交失败:", error);
        this.$modal.msgError("预约失败，请稍后重试");
      }
    },

    /** 验证预约表单 */
    validateReservationForm() {
      return new Promise((resolve) => {
        this.$refs.reservationForm.validate(valid => {
          resolve(valid);
        });
      });
    },

    /** 验证预约业务逻辑 */
    validateReservationBusiness() {
      if (!this.reservationForm.timeSlot) {
        return { isValid: false, message: "请选择预约时间段" };
      }

      if (!this.reservationForm.hisPatientId) {
        return { isValid: false, message: "患者编号不能为空" };
      }

      if (this.selectedExams.length === 0) {
        return { isValid: false, message: "请先选择要预约的检查项目" };
      }

      return { isValid: true };
    },

    /** 执行预约提交 */
    async performReservationSubmit() {
      try {
        this.loading = true;

        const requestIds = this.selectedExams.map(exam => exam.requestId);
        const data = {
          requestIds,
          planId: this.reservationForm.timeSlot.planId,
          planType: this.reservationForm.planType
        };

        const response = await batchSubmitAppointment(data);

        if (response.code === 200) {
          this.$modal.msgSuccess(`预约成功，共预约 ${requestIds.length} 个检查项目`);
          await this.handleReservationSuccess();
        } else {
          this.$modal.msgError(`预约失败：${response.msg}`);
        }
      } finally {
        this.loading = false;
      }
    },

    /** 处理预约成功后的操作 */
    async handleReservationSuccess() {
      // 更新已预约的检查状态
      this.selectedExams.forEach(exam => {
        exam.status = APPOINTMENT_STATUS.RESERVED;
      });

      // 检查是否所有检查都已预约
      this.checkAllExamsReserved();

      // 重置表单部分字段，但保留患者信息
      this.resetReservationForm(true);

      // 更新数据
      await Promise.all([
        this.getAvailableTimeSlots(),
        this.getList()
      ]);
    },

    /** 检查是否所有检查都已预约 */
    checkAllExamsReserved() {
      const allReserved = this.patientExams.every(exam => this.isReservedStatus(exam.status));
      if (allReserved) {
        this.$modal.msgSuccess("所有检查已全部预约完成");
        this.reservationOpen = false;
      }
    },

    /** 取消预约对话框 */
    cancelReservation() {
      this.reservationOpen = false;
      this.resetReservationForm();
    },

    // ==================== 预约管理方法 ====================

    /** 取消预约操作 */
    async handleCancelAppointment(row) {
      try {
        await this.$modal.confirm('确认要取消该预约吗？');
        this.loading = true;

        const appointmentId = await this.getAppointmentId(row.requestId);
        if (!appointmentId) {
          this.$modal.msgError('未找到相关预约信息');
          return;
        }

        await cancelReservation(appointmentId);
        this.$modal.msgSuccess('取消预约成功');
        await this.getList();
      } catch (error) {
        if (error !== 'cancel') {
          console.error("取消预约失败:", error);
          this.$modal.msgError('取消预约失败，请稍后重试');
        }
      } finally {
        this.loading = false;
      }
    },

    /** 查看预约操作 */
    async handleViewAppointment(row) {
      try {
        this.loading = true;
        const response = await listReservation({ requestId: row.requestId });

        if (response.rows && response.rows.length > 0) {
          const appointment = response.rows[0];
          this.showAppointmentInfo(appointment);
        } else {
          this.$modal.msgError('未找到相关预约信息');
        }
      } catch (error) {
        console.error("查看预约失败:", error);
        this.$modal.msgError('获取预约信息失败');
      } finally {
        this.loading = false;
      }
    },

    /** 获取预约ID */
    async getAppointmentId(requestId) {
      const response = await listReservation({ requestId });
      return response.rows && response.rows.length > 0 ? response.rows[0].appointmentId : null;
    },

    /** 显示预约信息 */
    showAppointmentInfo(appointment) {
      const planTypeLabel = this.getPlanTypeLabel(appointment.planType);
      const statusLabels = {
        0: '待检查',
        1: '已检查',
        2: '已取消'
      };

      this.$modal.msgInfo(
        `<div style="text-align: left;">
          <p><strong>预约信息：</strong></p>
          <p><strong>患者姓名：</strong>${appointment.patientName}</p>
          <p><strong>患者编号：</strong>${appointment.hisPatientId}</p>
          <p><strong>设备名称：</strong>${appointment.deviceName}</p>
          <p><strong>预约日期：</strong>${appointment.appointmentDate}</p>
          <p><strong>预约时间：</strong>${appointment.appointmentTime}</p>
          <p><strong>预约类型：</strong>${planTypeLabel}</p>
          <p><strong>状态：</strong>${statusLabels[appointment.status] || '未知'}</p>
        </div>`
      );
    },

    /** 选择患者检查部位 */
    selectExam(exam) {
      // 验证检查状态
      if (this.isReservedStatus(exam.status)) {
        this.$message.info("该检查已预约");
        return;
      }

      // 更新预约表单检查信息
      this.updateReservationFormWithExam(exam);

      // 处理科室变更
      this.handleDeptChangeForExam(exam);

      // 恢复或清空时间段选择
      this.restoreOrClearTimeSlot(exam);
    },

    /** 更新预约表单检查信息 */
    updateReservationFormWithExam(exam) {
      Object.assign(this.reservationForm, {
        requestId: exam.requestId,
        studyExamname: exam.studyExamname,
        studyBodypart: exam.bodyInfo?.bodyPartDictLabel || exam.studyBodypart || '',
        studyBodypartCode: exam.studyBodypartCode,
        bodyInfo: exam.bodyInfo,
        numberSource: exam.bodyInfo?.numberSource || ""
      });
    },

    /** 处理检查的科室变更 */
    handleDeptChangeForExam(exam) {
      const currentDept = this.reservationForm.studyPerformdept;
      const newDept = exam.studyPerformdept || '';

      // 如果执行科室发生变化且新检查有执行科室信息，才更新科室和设备
      if (currentDept !== newDept && exam.studyPerformdeptid) {
        this.setDefaultDept(exam.studyPerformdeptid, false);
      }
    },

    /** 恢复或清空时间段选择 */
    restoreOrClearTimeSlot(exam) {
      if (exam.timeSlot) {
        this.restoreTimeSlotSelection(exam);
      } else {
        this.clearTimeSlotSelectionAndAutoSelect();
      }
    },

    /** 恢复时间段选择 */
    restoreTimeSlotSelection(exam) {
      this.reservationForm.timeSlot = exam.timeSlot;

      // 恢复预约类型
      this.restorePlanType(exam);

      // 更新UI中的时间段选中状态
      this.updateTimeSlotUISelection(exam.timeSlot);
    },

    /** 恢复预约类型 */
    restorePlanType(exam) {
      if (exam.selectedPlanType !== undefined) {
        this.reservationForm.planType = exam.selectedPlanType;
      } else if (exam.timeSlot?.planType !== undefined) {
        this.reservationForm.planType = exam.timeSlot.planType;
      } else {
        this.reservationForm.planType = PLAN_TYPES.ORDINARY;
      }
    },

    /** 更新时间段UI选择状态 */
    updateTimeSlotUISelection(timeSlot) {
      if (!this.availableTimeSlots?.length) return;

      const matchedSlot = this.availableTimeSlots.find(slot =>
        slot.planId === timeSlot.planId
      );

      if (matchedSlot) {
        // 清空所有选中状态
        this.availableTimeSlots.forEach(slot => {
          slot.selected = false;
        });

        // 选中匹配的时间段
        this.selectTimeSlot(matchedSlot);
      }
    },

    /** 清空时间段选择并自动选择 */
    clearTimeSlotSelectionAndAutoSelect() {
      this.reservationForm.timeSlot = null;
      this.reservationForm.planType = PLAN_TYPES.ORDINARY;

      if (this.availableTimeSlots?.length > 0) {
        this.availableTimeSlots.forEach(slot => {
          slot.selected = false;
        });
        this.autoSelectAvailableOrdinarySlot();
      }
    },



    /** 科室变更事件但不重置已选设备和时间段 */
    async handleDeptChangeWithoutReset(deptId) {
      // 获取该科室下的设备列表
      if (deptId) {
        await this.getDeviceList(deptId);
        // handleDeviceChange是同步方法，不需要await
        this.handleDeviceChange();
      } else {
        this.deviceOptions = [];
      }
    },
  }
};
</script>

<style scoped>
/* 预约对话框样式 */
.reservation-container {
  height: 70%;
  overflow: hidden;
}

/* 左侧面板样式 */
.left-panel {
  border-right: 1px solid #ebeef5;
  height: 100%;
  overflow-y: auto;
  padding-right: 15px;
}

/* 患者信息样式 */
.patient-info {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
}

.info-item {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.5;
}

.info-item .label {
  color: #606266;
  font-weight: bold;
}

.info-item .value {
  color: #303133;
}

/* 设备列表样式 */
.section-title {
  font-size: 16px;
  font-weight: bold;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
  color: #303133;
}

.title-with-type {
  display: flex;
  align-items: center;
  gap: 15px;
}

.current-plan-type {
  display: flex;
  align-items: center;
  gap: 8px;
}

.plan-type-label {
  font-size: 14px;
  color: #606266;
  font-weight: normal;
}

.device-list-container {
  margin-top: 20px;
}

.device-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.device-list li {
  padding: 10px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 5px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
}

.device-list li:hover {
  background-color: #f5f7fa;
}

.device-list li.active {
  background-color: #ecf5ff;
  color: #409EFF;
  font-weight: bold;
}

.device-list li i {
  margin-right: 8px;
  color: #409EFF;
}

.no-devices {
  text-align: center;
  padding: 20px 0;
  color: #909399;
}

.no-devices i {
  font-size: 24px;
  margin-bottom: 10px;
  display: block;
}

/* 右侧面板样式 */
.right-panel {
  height: 100%;
  overflow-y: auto;
  padding-left: 15px;
}

/* 日期头部样式 */
.date-header {
  color: white;
  border-radius: 4px;
}

.current-date {
  font-size: 18px;
  font-weight: bold;
}

.week-day {
  margin-left: 10px;
  font-size: 16px;
}

/* 排班信息样式 */
.schedule-container {
  min-height: 400px;
}

.no-device-selected,
.no-date-selected,
.no-schedules {
  text-align: center;
  padding: 100px 0;
  color: #909399;
}

.no-device-selected i,
.no-date-selected i,
.no-schedules i {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}


.time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.time-slots .el-card {
  cursor: pointer;
  transition: all 0.3s;
}

.time-slots .el-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.time-slots .el-card.selected-slot {
  border-color: #409EFF;
  background-color: #ecf5ff;
}

.time-slots .el-card.disabled-slot {
  cursor: not-allowed;
  opacity: 0.6;
}

.time-range {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #303133;
}

.time-display {
  margin-bottom: 5px;
}

.disabled-reason {
  display: inline-block;
  font-size: 12px;
  color: #F56C6C;
  background-color: #FEF0F0;
  padding: 2px 6px;
  border-radius: 2px;
  font-weight: normal;
  text-align: center;
  width: 100%;
}

.disabled-reason-text {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: help;
}

.remaining-counts {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 10px;
}

.remaining-count-item {
  display: flex;
  justify-content: space-between;
}

.remaining-count-item .el-tag {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 5px;
}

.remaining-count-item .count {
  font-weight: bold;
}

.remaining-count-item.total {
  margin-top: 5px;
}

.remaining-count-item .el-tag.selected-type {
  font-weight: bold;
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}

.remaining-count-item .el-tag:hover {
  cursor: pointer;
  transform: scale(1.02);
  transition: all 0.2s;
}

.remaining-count-item .el-tag.disabled-tag {
  cursor: not-allowed;
  opacity: 0.6;
}

.remaining-count-item .el-tag.disabled-tag:hover {
  cursor: not-allowed;
  transform: none;
}

/* 验证错误信息样式 */
.validation-error {
  font-size: 11px;
  color: #F56C6C;
  background-color: #FEF0F0;
  padding: 2px 4px;
  border-radius: 2px;
  margin-top: 2px;
  text-align: center;
  line-height: 1.2;
}



/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-buttons .el-button {
  margin: 1px 0;
  padding: 2px 0;
}

.operation-buttons .el-button+.el-button {
  margin-left: 0;
}

/* 固定列样式 */
.el-table .fixed-right {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

/* 表格横向滚动样式 */
.el-table {
  overflow-x: auto;
}

/* 患者检查卡片区域样式 */
.patient-exams-container {
  margin: 20px 0;
}

.exams-scroll-container {
  overflow-x: auto;
  padding: 10px 0;
}

.exams-cards {
  display: flex;
  flex-wrap: nowrap;
  gap: 15px;
  padding-bottom: 10px;
}

.exam-card {
  min-width: 250px;
  max-width: 300px;
  flex: 0 0 auto;
  transition: all 0.3s;
}

.exam-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.exam-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 12px;
  border-left: 3px solid #409EFF;
  padding-left: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.exam-title .title-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.exam-title .title-row:last-child {
  margin-bottom: 0;
}

.exam-title .dept-label,
.exam-title .part-label {
  color: #606266;
  margin-right: 4px;
  flex-shrink: 0;
}

.exam-title .dept-value,
.exam-title .part-value {
  color: #409EFF;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
}

.exam-info {
  margin-bottom: 10px;
}

.exam-info p {
  margin: 8px 0;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.exam-info i {
  margin-right: 5px;
  color: #909399;
}

.exam-status {
  text-align: center;
  padding: 6px 0;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-weight: bold;
  color: #909399;
}

.exam-status.status-reserved {
  background-color: #f0f9eb;
  color: #67c23a;
}



/* 复选框样式 */
.exam-checkbox {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  padding: 8px;
  pointer-events: none;
  /* 禁用鼠标事件 */
  border-radius: 4px;
}

/* 移除复选框的悬停效果，因为现在是只读的 */

.exam-card-wrapper {
  cursor: pointer;
  transition: all 0.3s ease;
}

.exam-card-wrapper:hover .exam-card {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.exam-card-wrapper:active .exam-card {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.exam-card {
  position: relative;
  transition: all 0.3s ease;
}

/* 已预约的卡片样式 */
.exam-card-wrapper.status-reserved-wrapper {
  cursor: not-allowed;
}

.exam-card-wrapper.status-reserved-wrapper:hover .exam-card {
  transform: none;
  box-shadow: none;
}

.exam-card.status-reserved {
  opacity: 0.7;
  background-color: #f5f7fa;
}

/* 选中状态的卡片样式 */
.exam-card.checked-exam {
  border: 2px solid #67c23a;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
  background-color: #f0f9eb;
}

.exam-card.checked-exam .exam-title {
  border-left-color: #67c23a;
}

/* 标题区域样式 */
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
  padding: 15px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title-left {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.main-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.exam-statistics {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  font-size: 14px;
  color: #606266;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.stat-item:hover {
  background-color: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.stat-item i {
  color: #409EFF;
  font-size: 16px;
}

.stat-item-placeholder {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 20px;
  font-size: 14px;
  color: #909399;
  font-style: italic;
}

.stat-item-placeholder i {
  color: #C0C4CC;
  font-size: 16px;
}

/* 选择控制区域样式 */
.selection-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.selected-info {
  color: #409EFF;
  font-weight: bold;
  font-size: 14px;
  padding: 6px 12px;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 15px;
  border: 1px solid rgba(64, 158, 255, 0.3);
}

/* 批量预约按钮样式 */
.dialog-footer .el-button--success {
  background-color: #67c23a;
  border-color: #67c23a;
}

.dialog-footer .el-button--success:hover {
  background-color: #85ce61;
  border-color: #85ce61;
}
</style>
