<template>
  <div class="app-container">
    <el-card class="box-card">
      <div slot="header" class="clearfix">
        <span>待预约申请单列表</span>
      </div>

      <!-- 搜索区域 -->
      <el-form :model="queryParams" ref="queryForm" :inline="true" v-show="showSearch" label-width="68px">
        <el-form-item label="患者姓名" prop="patientName">
          <el-input v-model="queryParams.patientName" placeholder="请输入患者姓名" clearable style="width: 240px"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="身份证号" prop="patientIdentity">
          <el-input v-model="queryParams.patientIdentity" placeholder="请输入身份证号" clearable style="width: 240px"
            @keyup.enter.native="handleQuery" />
        </el-form-item>
        <el-form-item label="申请时间">
          <el-date-picker v-model="dateRange" style="width: 240px" value-format="yyyy-MM-dd" type="daterange"
            range-separator="-" start-placeholder="开始日期" end-placeholder="结束日期"></el-date-picker>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" @click="handleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" @click="resetQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <!-- 操作按钮区域 -->
      <el-row :gutter="10" class="mb8">
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-refresh" @click="handleSyncRequests"
            v-hasPermi="['reservation:studyRequest:sync']">同步申请单</el-button>
        </el-col>
        <el-col :span="1.5">
          <el-button type="primary" plain icon="el-icon-magic-stick" @click="handleAutoReservation"
            v-hasPermi="['reservation:studyRequest:reservation']" :disabled="multiple">自动预约</el-button>
        </el-col>
        <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
      </el-row>

      <!-- 申请单列表 -->
      <el-table v-loading="loading" :data="requestList" @selection-change="handleSelectionChange" border
        :max-height="500" style="width: 100%">
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="申请单号" align="center" prop="requestId" :show-overflow-tooltip="true" />
        <el-table-column label="患者姓名" align="center" prop="patientName" width="100" />
        <el-table-column label="患者编号" align="center" prop="hisPatientId" width="120" :show-overflow-tooltip="true" />
        <el-table-column label="性别" align="center" prop="patientSex" width="60">
          <template slot-scope="scope">
            <dict-tag :options="dict.type.sys_user_sex" :value="scope.row.patientSex" />
          </template>
        </el-table-column>
        <el-table-column label="年龄" align="center" width="60">
          <template slot-scope="scope">
            {{ calculateAge(scope.row.patientBirthday) }}
          </template>
        </el-table-column>
        <el-table-column label="身份证号" align="center" prop="patientIdentity" width="180" :show-overflow-tooltip="true" />
        <el-table-column label="联系电话" align="center" prop="patientTelephone" width="120" />
        <el-table-column label="检查类别" align="center" prop="studyExamkind" width="100" />
        <el-table-column label="项目名称" align="center" prop="studyExamname" :show-overflow-tooltip="true" />
        <el-table-column label="检查部位" align="center" prop="studyBodypart" width="100" :show-overflow-tooltip="true" />
        <el-table-column label="申请科室" align="center" prop="studyApplydept" width="120" />
        <el-table-column label="执行科室" align="center" prop="studyPerformdept" width="120" />
        <el-table-column label="申请医生" align="center" prop="studyApplicant" width="100" />
        <el-table-column label="申请时间" align="center" prop="studyApplydttm" width="150">
          <template slot-scope="scope">
            <span>{{ parseTime(scope.row.studyApplydttm) }}</span>
          </template>
        </el-table-column>
        <el-table-column label="状态" align="center" width="80">
          <template slot-scope="scope">
            <el-tag type="success" v-if="scope.row.status === 1">已预约</el-tag>
            <el-tag type="info" v-else>未预约</el-tag>
          </template>
        </el-table-column>
        <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150" fixed="right">
          <template slot-scope="scope">
            <div class="operation-buttons">
              <!-- 未预约状态显示预约按钮 -->
              <el-button v-if="scope.row.status !== 1" size="mini" type="text" icon="el-icon-edit"
                @click="handleReservation(scope.row)"
                v-hasPermi="['reservation:studyRequest:reservation']">预约</el-button>
              <!-- 已预约状态显示取消预约和查看预约按钮 -->
              <el-button v-if="scope.row.status === 1" size="mini" type="text" icon="el-icon-close"
                @click="handleCancelAppointment(scope.row)"
                v-hasPermi="['reservation:studyRequest:reservation']">取消预约</el-button>
              <el-button v-if="scope.row.status === 1" size="mini" type="text" icon="el-icon-view"
                @click="handleViewAppointment(scope.row)"
                v-hasPermi="['reservation:studyRequest:query']">查看预约</el-button>
              <!-- 所有状态都显示详情按钮 -->
              <el-button size="mini" type="text" icon="el-icon-view" @click="handleDetail(scope.row)"
                v-hasPermi="['reservation:studyRequest:query']">详情</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>

      <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum" :limit.sync="queryParams.pageSize"
        @pagination="getList" />
    </el-card>

    <!-- 申请单详情对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="800px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" label-width="100px">
        <!-- 患者基本信息 -->
        <el-divider content-position="left">患者基本信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="患者姓名" prop="patientName">
              <el-input v-model="form.patientName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="患者编号" prop="hisPatientId">
              <el-input v-model="form.hisPatientId" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="性别" prop="patientSex">
              <el-input v-model="form.patientSex" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="出生日期" prop="patientBirthday">
              <el-input v-model="form.patientBirthday" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="身份证号" prop="patientIdentity">
              <el-input v-model="form.patientIdentity" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="联系电话" prop="patientTelephone">
              <el-input v-model="form.patientTelephone" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="病人类型" prop="patientType">
              <el-input v-model="form.patientType" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="病人来源" prop="studySource">
              <el-input v-model="form.studySource" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="卡号" prop="patientCardno">
              <el-input v-model="form.patientCardno" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <!-- 申请信息 -->
        <el-divider content-position="left">申请信息</el-divider>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="申请单号" prop="requestId">
              <el-input v-model="form.requestId" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查类别" prop="studyExamkind">
              <el-input v-model="form.studyExamkind" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目名称" prop="studyExamname">
              <el-input v-model="form.studyExamname" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="检查部位" prop="studyBodypart">
              <el-input v-model="form.studyBodypart" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="检查方法" prop="studyMethod">
              <el-input v-model="form.studyMethod" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请时间" prop="studyApplydttm">
              <el-input v-model="form.studyApplydttm" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="申请科室" prop="studyApplydept">
              <el-input v-model="form.studyApplydept" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="申请医生" prop="studyApplicant">
              <el-input v-model="form.studyApplicant" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="执行科室" prop="studyPerformdept">
              <el-input v-model="form.studyPerformdept" disabled />
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="24">
            <el-form-item label="备注" prop="studyComment">
              <el-input type="textarea" v-model="form.studyComment" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 预约对话框 -->
    <el-dialog :title="'预约 - ' + reservationForm.patientName" :visible.sync="reservationOpen" width="65%" append-to-body
      :close-on-click-modal="false">
      <el-row :gutter="20" class="reservation-container">
        <!-- 左侧区域：执行科室和设备列表 -->
        <el-col :span="6" class="left-panel">
          <el-form ref="reservationForm" :model="reservationForm" :rules="rules" label-width="80px">
            <!-- 基本信息 -->
            <div class="patient-info">
              <div class="info-item">
                <span class="label">患者姓名：</span>
                <span class="value">{{ reservationForm.patientName }}</span>
              </div>
              <div class="info-item">
                <span class="label">患者编号：</span>
                <span class="value">{{ reservationForm.hisPatientId }}</span>
              </div>
              <div class="info-item" v-if="reservationForm.patientTelephone">
                <span class="label">手机号：</span>
                <span class="value">{{ reservationForm.patientTelephone }}</span>
              </div>
              <div class="info-item">
                <span class="label">检查项目：</span>
                <span class="value">{{ reservationForm.studyExamname }}</span>
              </div>
              <div class="info-item" v-if="reservationForm.studyPerformdept">
                <span class="label">执行科室：</span>
                <span class="value">{{ reservationForm.studyPerformdept }}</span>
              </div>
            </div>

            <!-- 预约日期选择 -->
            <el-form-item label="预约日期" prop="reservationDate">
              <el-date-picker v-model="reservationForm.reservationDate" type="date" placeholder="选择预约日期"
                style="width: 100%" :picker-options="datePickerOptions" @change="handleDateChange" />
            </el-form-item>

            <!-- 科室选择 -->
            <el-form-item label="选择科室" prop="deptId">
              <el-select v-model="reservationForm.deptId" placeholder="请选择科室" style="width: 100%"
                @change="handleDeptChange">
                <el-option v-for="item in deptOptions" :key="item.deptId" :label="item.deptName" :value="item.deptId" />
              </el-select>
            </el-form-item>

            <!-- 设备列表 -->
            <div class="device-list-container" v-if="deviceOptions.length > 0">
              <div class="section-title">设备列表</div>
              <ul class="device-list">
                <li v-for="device in deviceOptions" :key="device.deviceId"
                  :class="{ 'active': reservationForm.deviceId === device.deviceId }"
                  @click="selectDevice(device.deviceId)">
                  <i class="el-icon-monitor"></i>
                  <span>{{ device.deviceName }}</span>
                </li>
              </ul>
            </div>
            <div v-else class="no-devices">
              <i class="el-icon-info"></i>
              <span>请选择科室查看设备</span>
            </div>
          </el-form>
        </el-col>

        <!-- 右侧区域：日期和排班信息 -->
        <el-col :span="18" class="right-panel">
          <!-- 日期和星期显示 -->
          <div class="date-header" v-if="patientExams.length > 0">
            <!-- <div class="current-date">
              {{ formatDate(reservationForm.reservationDate) }}
              <span class="week-day">{{ getWeekDay(reservationForm.reservationDate) }}</span>
            </div> -->
            <div class="section-title">
              <div class="title-left">
                <span class="main-title">患者检查列表</span>
                <div class="exam-statistics" v-if="selectedExamsCount > 0">
                  <span class="stat-item">
                    <i class="el-icon-document"></i>
                    已选检查：{{ selectedExamsCount }} 项
                  </span>
                  <span class="stat-item">
                    <i class="el-icon-tickets"></i>
                    需占号数：{{ totalSelectedNumbers }} 个
                  </span>
                </div>
                <div class="exam-statistics" v-else>
                  <span class="stat-item-placeholder">
                    <i class="el-icon-info"></i>
                    请选择要预约的检查项目
                  </span>
                </div>
              </div>
              <div class="selection-controls">
                <el-checkbox v-model="selectAllChecked" @change="handleSelectAll"
                  :indeterminate="selectAllIndeterminate" style="margin-right: 15px;">
                  相同科室全选
                </el-checkbox>

              </div>
            </div>
            <div class="exams-scroll-container">
              <div class="exams-cards">
                <div v-if="patientExams.length === 0" class="no-exams">
                  <p>没有检查数据</p>
                </div>
                <div class="exam-card-wrapper" :class="{ 'status-reserved-wrapper': exam.status === 1 }"
                  @click="handleExamCardClick(exam)" v-for="(exam, index) in patientExams" :key="index">
                  <el-card shadow="hover" class="exam-card"
                    :class="{ 'checked-exam': exam.checked, 'status-reserved': exam.status === 1 }">
                    <div class="exam-checkbox">
                      <el-checkbox v-model="exam.checked" @click.stop.prevent :disabled="true">
                      </el-checkbox>
                    </div>
                    <div class="exam-title">
                      <div class="title-row">
                        <span class="dept-label">科室：</span>
                        <span class="dept-value">{{ exam.studyPerformdept }}</span>
                      </div>
                      <div class="title-row">
                        <span class="part-label">部位：</span>
                        <span class="part-value">{{ exam.studyExamname }}</span>
                      </div>
                    </div>

                    <div class="exam-info">
                      <p><i class="el-icon-location"></i> 检查部位：{{ exam.bodyInfo && exam.bodyInfo.bodyPartDictLabel ||
                        exam.studyBodypart || '未指定' }}，占号：{{ exam.bodyInfo && exam.bodyInfo.numberSource }} </p>
                    </div>
                    <div class="exam-status" :class="{ 'status-reserved': exam.status === 1 }">
                      {{ exam.status === 1 ? '已预约' : '未预约' }}
                    </div>
                  </el-card>
                </div>
              </div>
            </div>
          </div>

          <!-- 设备排班信息 -->
          <div class="schedule-container">
            <div v-if="!reservationForm.deviceId" class="no-device-selected">
              <i class="el-icon-warning"></i>
              <span>请在左侧选择设备查看排班信息</span>
            </div>
            <div v-else-if="!reservationForm.reservationDate" class="no-date-selected">
              <i class="el-icon-date"></i>
              <span>请选择预约日期</span>
            </div>
            <div v-else-if="availableTimeSlots.length === 0" class="no-schedules">
              <i class="el-icon-time"></i>
              <span>所选日期没有可用的排班计划</span>
            </div>
            <div v-else class="time-slots-wrapper">
              <div class="section-title">
                <div class="title-with-type">
                  <span>可预约时间段</span>
                  <div class="current-plan-type">
                    <span class="plan-type-label">当前选择：</span>
                    <el-tag size="small" type="success" v-if="reservationForm.planType === 0">普通号</el-tag>
                    <el-tag size="small" type="danger" v-if="reservationForm.planType === 1">急诊号</el-tag>
                    <el-tag size="small" type="primary" v-if="reservationForm.planType === 2">预留号</el-tag>
                  </div>
                </div>
              </div>
              <div class="time-slots">
                <el-card v-for="(slot, index) in availableTimeSlots" :key="index"
                  :class="{ 'selected-slot': slot.selected, 'disabled-slot': slot.disabled }" shadow="hover">
                  <div class="time-range">
                    <div class="time-display">{{ slot.startTime }} - {{ slot.endTime }}</div>
                    <div v-if="slot.disabled && slot.disabledReason" class="disabled-reason">
                      <el-tooltip :content="slot.disabledReasonFull || slot.disabledReason" placement="top"
                        :disabled="!slot.disabledReasonFull">
                        <span class="disabled-reason-text">{{ slot.disabledReason }}</span>
                      </el-tooltip>
                    </div>
                  </div>
                  <div class="remaining-counts">
                    <div class="remaining-count-item" @click.stop="!slot.disabled && selectPlanType(slot, 0)">
                      <el-tag type="success" size="small"
                        :class="{ 'selected-type': reservationForm.planType === 0 && slot.selected, 'disabled-tag': slot.disabled || slot.ordinaryRemaining <= 0 }">
                        普通号: <span class="count">{{ slot.ordinaryCount || 0 }}/{{ slot.ordinaryPlan }}</span>
                      </el-tag>
                    </div>
                    <div class="remaining-count-item" @click.stop="!slot.disabled && selectPlanType(slot, 1)">
                      <el-tag type="danger" size="small"
                        :class="{ 'selected-type': reservationForm.planType === 1 && slot.selected, 'disabled-tag': slot.disabled || slot.emergencyRemaining <= 0 }">
                        急诊号: <span class="count">{{ slot.emergencyCount || 0 }}/{{ slot.emergencyPlan }}</span>
                      </el-tag>
                    </div>
                    <div class="remaining-count-item" @click.stop="!slot.disabled && selectPlanType(slot, 2)"
                      v-if="slot.reservePlan != -1">
                      <el-tag type="primary" size="small"
                        :class="{ 'selected-type': reservationForm.planType === 2 && slot.selected, 'disabled-tag': slot.disabled || slot.reserveRemaining <= 0 }">
                        保留号: <span class="count">{{ slot.reserveCount || 0 }}/{{ slot.reservePlan }}</span>
                      </el-tag>
                    </div>
                    <div class="remaining-count-item total">
                      <el-tag size="small">
                        总计: <span class="count" v-if="slot.reservePlan == -1">{{ slot.totalCount || 0 }}/{{
                          slot.ordinaryPlan + slot.emergencyPlan }} </span>
                        <span class="count" v-else>{{ slot.totalCount || 0 }}/{{ slot.totalPlan }}</span>
                        <!-- 总计: <span class="count">{{ slot.totalCount || 0 }}/{{ slot.totalPlan }}</span> -->
                      </el-tag>
                    </div>
                  </div>
                </el-card>
              </div>
            </div>


          </div>
        </el-col>
      </el-row>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitReservation" :disabled="!reservationForm.timeSlot">确认预约</el-button>
        <el-button @click="cancelReservation">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listStudyRequest, getStudyRequest, syncStudyRequests } from "@/api/reservation/studyRequest";
import { listDept } from "@/api/system/dept";
import { listModality } from "@/api/device/device";
import { listDevicePlan } from "@/api/device/plan";
import { cancelReservation, listReservation } from "@/api/reservation/reservation";
import { getAppointmentCount, autoSubmitAppointment, validateAppointment, batchSubmitAppointment } from "@/api/reservation/appointment";
import { parseTime } from "@/utils/ruoyi";

export default {
  name: "StudyRequestList",
  dicts: ['sys_user_sex', 'study_body_part_code'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 申请单表格数据
      requestList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示预约弹出层
      reservationOpen: false,
      // 日期范围
      dateRange: [],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        patientName: undefined,
        patientIdentity: undefined,
        studyApplydttm: undefined,
        status: 0
      },
      // 表单参数
      form: {},
      // 预约表单
      reservationForm: {
        requestId: "",
        hisPatientId: "", // 患者ID，使用 hisPatientId
        patientName: "",
        studyExamname: "",
        reservationDate: "",
        deptId: "",
        deviceId: "",
        timeSlot: null,
        planType: 0 // 默认为普通号，0普通号，1急诊号，2预留号
      },
      // 表单校验规则
      rules: {
        hisPatientId: [
          { required: true, message: "患者编号不能为空", trigger: "blur" }
        ],
        reservationDate: [
          { required: true, message: "请选择预约日期", trigger: "change" }
        ],
        deptId: [
          { required: true, message: "请选择预约科室", trigger: "change" }
        ],
        deviceId: [
          { required: true, message: "请选择预约设备", trigger: "change" }
        ]
      },
      // 科室选项
      deptOptions: [],
      // 设备选项
      deviceOptions: [],
      // 可用时间段
      availableTimeSlots: [],
      // 日期选择器配置
      datePickerOptions: {
        disabledDate(time) {
          const today = new Date();
          today.setHours(0, 0, 0, 0); // 设置为今天的开始时间
          const sevenDaysLater = new Date(today);
          sevenDaysLater.setDate(today.getDate() + 7); // 今天开始的7天后
          // 禁用今天之前和7天后的日期
          return time.getTime() < today.getTime() || time.getTime() >= sevenDaysLater.getTime();
        }
      },
      // 患者检查信息
      patientExams: [],
      // 多选相关数据
      selectAllChecked: false,
      selectAllIndeterminate: false,
      selectSameChecked: false
    };
  },
  computed: {
    // 可用的检查（未预约的）
    availableExams() {
      return this.patientExams.filter(exam => exam.status !== 1);
    },
    // 已选中的检查
    selectedExams() {
      return this.availableExams.filter(exam => exam.checked);
    },
    // 计算选中的检查数量
    selectedExamsCount() {
      return this.selectedExams.length;
    },
    // 计算总占号数量
    totalSelectedNumbers() {
      return this.selectedExams.reduce((total, exam) => {
        const numberSource = exam.bodyInfo?.numberSource ? parseInt(exam.bodyInfo.numberSource) : 1;
        return total + numberSource;
      }, 0);
    },
    // 是否有选中的检查
    hasSelectedExam() {
      return this.selectedExams.length > 0;
    }
  },
  created() {
    this.getList();
    this.getDeptList();
  },
  methods: {
    /** 查询申请单列表 */
    getList() {
      this.loading = true;
      listStudyRequest(this.addDateRange(this.queryParams, this.dateRange)).then(response => {
        this.requestList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    /** 计算年龄 */
    calculateAge(birthday) {
      if (!birthday) {
        return "";
      }
      const birthDate = new Date(birthday);
      const today = new Date();
      let age = today.getFullYear() - birthDate.getFullYear();
      const m = today.getMonth() - birthDate.getMonth();
      if (m < 0 || (m === 0 && today.getDate() < birthDate.getDate())) {
        age--;
      }
      return age + "岁";
    },
    /** 取消按钮 */
    cancel() {
      this.open = false;
      this.reset();
    },
    /** 表单重置 */
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    /** 多选框选中数据 */
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.requestId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 同步申请单 */
    handleSyncRequests() {
      this.$modal.confirm('确认同步外部系统的申请单数据吗？').then(() => {
        this.loading = true;
        return syncStudyRequests();
      }).then(response => {
        this.$modal.msgSuccess("同步成功，共同步 " + response.data + " 条数据");
        this.getList();
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },

    /** 自动预约按钮操作 */
    handleAutoReservation() {
      if (this.ids.length === 0) {
        this.$modal.msgWarning("请至少选择一个申请单进行自动预约");
        return;
      }

      // 检查选中的申请单是否已经预约
      const selectedRows = this.requestList.filter(row => this.ids.includes(row.requestId));
      const alreadyReserved = selectedRows.filter(row => row.status === 1);

      if (alreadyReserved.length > 0) {
        this.$modal.msgWarning("您选择的申请单中有 " + alreadyReserved.length + " 个已经预约，请重新选择");
        return;
      }

      this.$modal.confirm('确认对选中的 ' + this.ids.length + ' 个申请单进行自动预约吗？').then(() => {
        this.loading = true;
        return autoSubmitAppointment(this.ids);
      }).then(response => {
        if (response.code === 200) {
          this.$modal.msgSuccess("自动预约成功，共预约 " + response.data + " 个申请单");
        } else {
          this.$modal.msgError("自动预约失败：" + response.msg);
        }
        this.getList();
        this.loading = false;
      }).catch(error => {
        console.error("自动预约失败:", error);
        this.$modal.msgError("自动预约失败，请稍后重试");
        this.loading = false;
      });
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.reset();
      const requestId = row.requestId || this.ids;
      getStudyRequest(requestId).then(response => {
        this.form = response.data;
        // 格式化日期
        if (this.form.studyApplydttm) {
          this.form.studyApplydttm = parseTime(this.form.studyApplydttm);
        }
        this.open = true;
        this.title = "申请单详情";
      });
    },
    /** 预约按钮操作 */
    handleReservation(row) {
      // 检查是否已经预约
      if (row.status === 1) {
        this.$modal.msgWarning("该申请单已经预约，无法再次预约");
        return;
      }

      // 检查患者ID是否存在
      if (!row.hisPatientId) {
        this.$modal.msgError("患者编号不存在，无法进行预约");
        return;
      }

      this.reservationForm = {
        requestId: row.requestId,
        hisPatientId: row.hisPatientId, // 使用 hisPatientId 作为患者ID
        patientName: row.patientName,
        patientTelephone: row.patientTelephone, // 添加患者手机号
        patientSex: row.patientSex, // 添加患者性别
        studyExamname: row.studyExamname,
        studyPerformdept: row.studyPerformdept, // 添加执行科室信息
        reservationDate: parseTime(new Date(), '{y}-{m}-{d}'), // 默认显示今天
        deptId: "",
        deviceId: "",
        timeSlot: null,
        planType: 0 // 默认为普通号
      };
      this.availableTimeSlots = [];
      this.deviceOptions = [];


      // 先加载科室列表，然后再打开预约对话框
      this.getDeptList().then(() => {
        // 自动选择科室
        if (row.studyPerformdeptid) {
          this.setDefaultDept(row.studyPerformdeptid);
        } else if (row.studyPerformdept) {
          const matchedDept = this.deptOptions.find(dept => dept.deptName === row.studyPerformdept);
          if (matchedDept) {
            this.setDefaultDept(matchedDept.deptId);
          }
        }

        // 打开预约对话框
        this.reservationOpen = true;
      });
      // 加载患者的所有检查申请单
      this.loadPatientExams(row.hisPatientId);

    },

    /** 加载患者的所有检查申请单 */
    loadPatientExams(patientId) {
      if (!patientId) {
        this.patientExams = [];
        return;
      }

      this.loading = true;
      // 查询该患者的所有申请单
      listStudyRequest({ hisPatientId: patientId, status: 0 }).then(response => {
        this.patientExams = response.rows || [];
        const bodyPartDict = this.dict.type.study_body_part_code;
        this.patientExams.forEach(exam => {
          this.$set(exam, 'checked', false);

          if (bodyPartDict && exam.studyBodypartCode) {
            const matchedDict = bodyPartDict.find(dict => dict.value === exam.studyBodypartCode);
            if (matchedDict) {
              exam.bodyInfo = {
                bodyPartDictLabel: matchedDict.label,
                bodyPartDictValue: matchedDict.value,
                numberSource: matchedDict.raw.standby1
              };
            }
          }
        })

        this.resetSelectionState();

        // 自动勾选与当前选择科室相同的检查项目
        this.autoSelectSameDeptExams();

        this.loading = false;
      }).catch(() => {
        this.patientExams = [];
        this.loading = false;
      });
    },

    /** 重置选择状态 */
    resetSelectionState() {
      this.selectAllChecked = false;
      this.selectAllIndeterminate = false;
      this.selectSameChecked = false;
    },

    /** 自动勾选与当前选择科室相同的检查项目 */
    autoSelectSameDeptExams() {
      console.log("点击了");
      // 获取当前选择的科室名称
      const currentDeptId = this.reservationForm.deptId;
      if (!currentDeptId) {
        return;
      }

      // 找到对应的科室名称
      const selectedDept = this.deptOptions.find(dept => dept.deptId === currentDeptId);
      if (!selectedDept) {
        return;
      }

      const currentDeptName = selectedDept.deptName;

      // 自动勾选与当前科室相同的检查项目
      this.patientExams.forEach(exam => {
        console.log(exam.studyPerformdept, currentDeptName);
        if (exam.status !== 1 && exam.studyPerformdept === currentDeptName) {
          this.$set(exam, 'checked', true);
        }
      });

      // 更新全选状态
      this.updateSelectAllState();
    },



    /** 检查是否可以选择该检查 */
    canSelectExam(exam, newState) {
      // 如果检查状态为已预约，则提示警告信息，并返回false
      if (exam.status === 1) {
        this.$message.warning('该检查已预约，无法操作');
        return false;
      }

      if (!newState) return true; // 取消选择总是允许的

      const currentDept = exam.studyPerformdept || '';
      const otherDeptSelected = this.selectedExams.find(selectedExam =>
        selectedExam.requestId !== exam.requestId &&
        selectedExam.studyPerformdept !== currentDept
      );

      if (otherDeptSelected) {
        this.$message.warning(`只能选择相同科室 的检查项目`);
        return false;
      }

      return true;
    },

    /** 处理检查卡片点击 */
    // 处理考试卡片点击事件
    handleExamCardClick(exam) {
      //加一个用户等待事件
      this.$modal.loading('加载中...');
      // 获取新的选中状态
      // 获取新的选中状态
      const newCheckedState = !exam.checked;
      console.log("asdasdassdasdas");
      // 检查是否可以选择该检查
      if (!this.canSelectExam(exam, newCheckedState)) {
        this.$modal.closeLoading();
        return;
      }
      // 设置新的选中状态
      this.$set(exam, 'checked', newCheckedState);
      // 清空已选择的时间段
      this.reservationForm.timeSlot = null;
      // 情况选择的时间段类型
      this.reservationForm.planType = null;
      // 如果新的选中状态为true，则选中
      if (newCheckedState) {
        this.selectExam(exam);
        this.getAvailableTimeSlots();
      } else {

        // 否则，处理日期改变
        this.handleDateChange();
      }
      this.$modal.closeLoading();
      // 更新全选状态
      this.updateSelectAllState();
    },

    /** 更新全选状态 */
    updateSelectAllState() {
      const selectedCount = this.selectedExamsCount;
      const availableCount = this.availableExams.length;

      if (selectedCount === 0) {
        this.selectAllChecked = false;
        this.selectAllIndeterminate = false;
      } else if (selectedCount === availableCount) {
        this.selectAllChecked = true;
        this.selectAllIndeterminate = false;
      } else {
        this.selectAllChecked = false;
        this.selectAllIndeterminate = true;
      }
    },

    /** 处理全选 */
    handleSelectAll(checked) {
      if (checked) {
        const currentDeptId = this.reservationForm.deptId;
        if (!currentDeptId) {
          return;
        }
        // 找到对应的科室名称
        const targetDept = this.deptOptions.find(dept => dept.deptId === currentDeptId);
        if (!targetDept) {
          return;
        }

        // 只选择目标科室的检查
        this.availableExams.forEach(exam => {
          this.$set(exam, 'checked', exam.studyPerformdept === targetDept.deptName);
        });
      } else {
        // 取消所有选择
        this.availableExams.forEach(exam => {
          this.$set(exam, 'checked', false);
        });
      }

      this.selectAllIndeterminate = false;
    },

    /** 处理相同全选 */
    handleSelectSame(checked) {
      if (this.selectedExams.length === 0) {
        this.$modal.msgWarning('请先勾选一个检查项目');
        this.selectSameChecked = false;
        return;
      }

      const currentDept = this.selectedExams[0].studyPerformdept;
      if (!currentDept) {
        this.$modal.msgWarning('选中的检查项目没有科室信息');
        this.selectSameChecked = false;
        return;
      }

      // 选择相同科室的所有检查
      this.availableExams.forEach(exam => {
        if (exam.studyPerformdept === currentDept) {
          this.$set(exam, 'checked', checked);
        }
      });

      this.updateSelectAllState();
    },


    /** 获取科室列表 */
    getDeptList() {
      return listDept().then(response => {
        this.deptOptions = response.data;
        return this.deptOptions;
      });
    },
    /** 设置默认科室 */
    setDefaultDept(deptId, resetDeviceAndTime = true) {
      const deptIdNum = parseInt(deptId, 10);
      const matchedDept = this.deptOptions.find(dept => dept.deptId === deptIdNum);

      this.reservationForm.deptId = matchedDept ? matchedDept.deptId : deptIdNum;
      this.reservationForm.studyPerformdept = matchedDept.deptName;
      // 根据参数决定是否重置设备和时间
      if (resetDeviceAndTime) {
        this.handleDeptChange(this.reservationForm.deptId);
      } else {
        this.handleDeptChangeWithoutReset(this.reservationForm.deptId);
      }
    },

    /** 科室变更事件 */
    handleDeptChange(deptId) {
      // 清空设备选择和时间段
      this.reservationForm.deviceId = "";
      this.reservationForm.timeSlot = null;
      this.availableTimeSlots = [];

      // 获取该科室下的设备列表
      if (deptId) {
        this.getDeviceList(deptId);
      } else {
        this.deviceOptions = [];
      }
    },
    /** 获取设备列表 */
    async getDeviceList(deptId) {
      try {
        this.loading = true;
        const response = await listModality({ deptId: deptId });
        this.deviceOptions = response.rows || [];
        // 如果有设备，默认选中第一个
        if (response.rows.length > 0) {
          this.reservationForm.deviceId = response.rows[0].deviceId;
          // 自动触发设备变更事件，加载可预约时间段
          this.handleDeviceChange();
        }

        this.loading = false;
        return this.deviceOptions;
      } catch (error) {
        console.error("获取设备列表失败:", error);
        this.loading = false;
        this.$modal.msgError("获取设备列表失败");
        return [];
      }
    },
    /** 日期变更事件 */
    handleDateChange() {
      // 如果已选择设备，则重新获取可用时间段
      if (this.reservationForm.deviceId) {
        this.getAvailableTimeSlots();
      }
      // 清空已选择的时间段
      this.reservationForm.timeSlot = null;
    },


    /** 设备变更事件 */
    handleDeviceChange() {
      this.reservationForm.timeSlot = null;
      if (this.reservationForm.reservationDate) {
        this.getAvailableTimeSlots();
      } else {
        this.availableTimeSlots = [];
      }
    },
    /** 创建时间段对象 */
    createTimeSlot(plan) {
      const timeSlot = {
        startTime: plan.stime,
        endTime: plan.etime,
        planId: plan.planId,
        disabled: false,
        selected: false,
        ordinaryPlan: plan.ordinaryPlan,
        emergencyPlan: plan.emergencyPlan,
        reservePlan: plan.reservePlan,
        totalPlan: plan.totalPlan,
        ordinaryCount: 0,
        emergencyCount: 0,
        reserveCount: 0,
        totalCount: 0
      };

      // 检查是否已过期
      const now = new Date();
      const planDate = new Date(parseTime(this.reservationForm.reservationDate, '{y}-{m}-{d}'));
      const [hour, minute] = plan.etime.split(':').map(Number);
      planDate.setHours(hour, minute, 0, 0);

      if (planDate < now) {
        timeSlot.disabled = true;
        timeSlot.disabledReason = '已过期';
      }

      return timeSlot;
    },

    /** 更新时间段预约数量 */
    updateTimeSlotCounts(timeSlot, countData) {
      countData.forEach(item => {
        if (item.planId === timeSlot.planId) {
          switch (item.planType) {
            case 0: timeSlot.ordinaryCount = item.appointmentCount || 0; break;
            case 1: timeSlot.emergencyCount = item.appointmentCount || 0; break;
            case 2: timeSlot.reserveCount = item.appointmentCount || 0; break;
          }
        }
      });

      // 计算总数和剩余数量
      timeSlot.totalCount = timeSlot.ordinaryCount + timeSlot.emergencyCount + timeSlot.reserveCount;
      timeSlot.ordinaryRemaining = Math.max(0, timeSlot.ordinaryPlan - timeSlot.ordinaryCount);
      timeSlot.emergencyRemaining = Math.max(0, timeSlot.emergencyPlan - timeSlot.emergencyCount);
      timeSlot.reserveRemaining = Math.max(0, timeSlot.reservePlan - timeSlot.reserveCount);
      timeSlot.remainingCount = Math.max(0, timeSlot.totalPlan - timeSlot.totalCount);

      // 检查是否已约满
      if (timeSlot.remainingCount <= 0 && !timeSlot.disabledReason) {
        timeSlot.disabled = true;
        timeSlot.disabledReason = '已约满';
      }
    },

    /** 获取可用时间段 */
    async getAvailableTimeSlots() {
      console.log("获取可用时间段");
      if (!this.reservationForm.deviceId || !this.reservationForm.reservationDate) {
        return;
      }

      const params = {
        deviceId: this.reservationForm.deviceId,
        planDate: parseTime(this.reservationForm.reservationDate, '{y}-{m}-{d}')
      };

      try {
        // 设置加载状态为true
        this.loading = true;
        // 调用listDevicePlan函数，获取设备计划列表
        const response = await listDevicePlan(params);
        // 获取设备计划列表中的行数据
        const plans = response.rows || [];

        // 将设备计划列表中的每一项转换为时间段
        this.availableTimeSlots = plans.map(plan => this.createTimeSlot(plan));

        // 获取所有时间段的预约数量
        const countPromises = this.availableTimeSlots.map(async (timeSlot) => {
          try {
            // 构造获取预约数量的参数
            const countParams = {
              deviceId: this.reservationForm.deviceId,
              planId: timeSlot.planId,
              appointmentDate: parseTime(this.reservationForm.reservationDate, '{y}-{m}-{d}'),
              _t: new Date().getTime()
            };
            // 调用getAppointmentCount函数，获取预约数量
            const countResponse = await getAppointmentCount(countParams);

            // 如果获取预约数量成功，则更新时间段的预约数量
            if (countResponse.code === 200 && countResponse.data) {
              this.updateTimeSlotCounts(timeSlot, countResponse.data);
            }
          } catch (error) {
            // 使用默认值
            timeSlot.ordinaryRemaining = timeSlot.ordinaryPlan;
            timeSlot.emergencyRemaining = timeSlot.emergencyPlan;
            timeSlot.reserveRemaining = timeSlot.reservePlan;
            timeSlot.remainingCount = timeSlot.totalPlan;
          }
        });

        await Promise.all(countPromises);


        //只有当有已选择的项目时，才自动选择普通号可用的时间段
        if (this.selectedExams.length >= 1) {
          // 验证时间段是否可以预约
          await this.validateTimeSlots();
          // 自动选择普通号可用的时间段
          this.autoSelectAvailableOrdinarySlot();
        }


        this.loading = false;
      } catch (error) {
        this.loading = false;
        console.error('获取时间段失败:', error);
      }
    },

    /** 验证时间段是否可以预约 */
    async validateTimeSlots() {
      if (!this.availableTimeSlots.length || !this.selectedExams.length) {
        return;
      }

      // 为每个已选择的检查项目验证每个时间段
      const validationPromises = [];

      for (const exam of this.selectedExams) {
        for (const slot of this.availableTimeSlots) {
          // 跳过已经被禁用的时间段
          if (slot.disabled) {
            continue;
          }

          // 只验证普通号（planType = 0）
          const validationPromise = this.validateSingleSlot(exam.requestId, slot.planId, 0)
            .then(result => {
              return {
                requestId: exam.requestId,
                planId: slot.planId,
                isValid: result.isValid,
                reason: result.reason
              };
            })
            .catch(error => {
              console.error(`验证失败 - 申请单: ${exam.requestId}, 时间段: ${slot.planId}`, error);
              return {
                requestId: exam.requestId,
                planId: slot.planId,
                isValid: false,
                reason: '验证接口调用失败'
              };
            });

          validationPromises.push(validationPromise);
        }
      }

      try {
        const validationResults = await Promise.all(validationPromises);
        this.processValidationResults(validationResults);
      } catch (error) {
        console.error('批量验证时间段失败:', error);
      }
    },

    /** 验证单个时间段 */
    async validateSingleSlot(requestId, planId, planType) {
      try {
        const response = await validateAppointment({
          requestId: requestId,
          planId: planId,
          planType: planType
        });

        // 通过 msg 字段判断：msg 为空表示可预约，有内容表示不可预约
        const isValid = !response.msg || response.msg.trim() === '';

        return {
          isValid: isValid,
          reason: response.msg || ''
        };
      } catch (error) {
        return {
          isValid: false,
          reason: error.message || '验证接口调用失败'
        };
      }
    },

    /** 处理验证结果 */
    processValidationResults(validationResults) {
      // 按时间段分组验证结果
      const slotValidations = {};

      validationResults.forEach(result => {
        const key = result.planId;
        if (!slotValidations[key]) {
          slotValidations[key] = [];
        }
        slotValidations[key].push(result);
      });

      // 更新时间段的验证状态
      this.availableTimeSlots.forEach(slot => {
        const validations = slotValidations[slot.planId] || [];

        // 如果任何一个选中的检查项目不能预约这个时间段，则禁用整个时间段
        const hasInvalidExam = validations.some(v => !v.isValid);
        const invalidReasons = validations.filter(v => !v.isValid).map(v => v.reason);

        if (hasInvalidExam) {
          slot.disabled = true;
          // 保存完整的禁用原因
          const fullReason = invalidReasons[0] || '验证失败';
          slot.disabledReasonFull = fullReason;

          // 设置显示的禁用原因，如果原因太长则截断并添加省略号
          slot.disabledReason = fullReason //fullReason.length > 20 ? fullReason.substring(0, 20) + '...' : fullReason;
        }
      });
    },



    /** 自动选择普通号可用的时间段 */
    autoSelectAvailableOrdinarySlot() {
      // 如果用户已经选择了时间段，不进行自动选择
      if (this.reservationForm.timeSlot) {
        return;
      }

      // 获取所有已选择检查项目的总号源数量
      const totalSelectedNumbers = this.totalSelectedNumbers;

      // 如果没有选择任何检查项目，使用默认值1
      const requiredNumbers = totalSelectedNumbers > 0 ? totalSelectedNumbers : 1;

      // 筛选出符合条件的时间段：未被禁用且普通号剩余数量足够
      const availableSlots = this.availableTimeSlots.filter(slot => {
        return !slot.disabled && // 未被禁用（包括未过期和验证通过）
          slot.ordinaryRemaining >= requiredNumbers; // 普通号剩余数量足够
      });

      // 如果有可用的时间段，自动选择第一个
      if (availableSlots.length > 0) {
        const firstAvailableSlot = availableSlots[0];

        // 设置预约类型为普通号
        this.reservationForm.planType = 0;

        // 自动选择这个时间段
        this.selectPlanType(firstAvailableSlot, 0);

        console.log(`自动选择时间段: ${firstAvailableSlot.startTime}-${firstAvailableSlot.endTime}, 普通号剩余: ${firstAvailableSlot.ordinaryRemaining}, 总需要号源: ${requiredNumbers}`);
      }
    },

    /** 选择可约时间段 */
    selectTimeSlot(slot) {
      if (slot.disabled) {
        return;
      }
      // 检查选择的号类型是否有剩余
      const type = this.reservationForm.planType;
      if (type === 0 && slot.ordinaryRemaining <= 0) {
        this.$modal.msgWarning('普通号已经预约满了，请选择其他类型的号');
        return;
      } else if (type === 1 && slot.emergencyRemaining <= 0) {
        this.$modal.msgWarning('急诊号已经预约满了，请选择其他类型的号');
        return;
      } else if (type === 2 && slot.reserveRemaining <= 0) {
        this.$modal.msgWarning('保留号已经预约满了，请选择其他类型的号');
        return;
      }

      // 取消之前选择的时间段
      this.availableTimeSlots.forEach(item => {
        item.selected = false;
      });

      // 选中当前时间段
      slot.selected = true;

      // 确保时间段对象中包含预约类型信息
      if (slot.planType === undefined && this.reservationForm.planType !== undefined) {
        slot.planType = this.reservationForm.planType;
      }

      this.reservationForm.timeSlot = slot;

      // 将选择的时间段保存到当前选中的检查记录中
      if (this.reservationForm.requestId) {
        const patient = this.patientExams.find(item => item.requestId === this.reservationForm.requestId);
        if (patient) {
          // 保存完整的时间段对象，以便在切换检查时能够完整恢复
          const timeSlotCopy = JSON.parse(JSON.stringify(slot));

          // 确保时间段对象中也包含预约类型信息
          if (this.reservationForm.planType !== undefined) {
            timeSlotCopy.planType = this.reservationForm.planType;
            // 同时单独保存预约类型，便于直接访问
            patient.selectedPlanType = this.reservationForm.planType;
          }

          patient.timeSlot = timeSlotCopy;
          console.log("保存时间段和预约类型：", patient.timeSlot.planId, patient.selectedPlanType);
          console.log("部位：" + patient.studyBodypart + " 选择的时间段：" + patient.timeSlot.planId);
        }
      }
    },

    /** 选择预约类型 普通还是急诊还是绿通*/
    selectPlanType(slot, type) {
      // 如果时间段被禁用，不允许选择
      if (slot.disabled) {
        return;
      }
      console.log(slot);
      let selectCont = 1;// 选择的部位占用的号源
      const patient = this.patientExams.find(item => item.requestId === this.reservationForm.requestId);
      if (patient) {
        // 保存预约类型
        patient.selectedPlanType = type;
        // 确保时间段对象中也包含预约类型信息
        if (patient.timeSlot) {
          patient.timeSlot.planType = type;
        }
        //selectCont = Number(patient.bodyInfo.numberSource);
        selectCont = this.totalSelectedNumbers;
      }

      // 检查选择的号类型是否有剩余
      if (type === 0 && slot.ordinaryRemaining - selectCont < 0) {
        this.$modal.msgWarning('普通号余量不足，请选择其他类型的号');
        return;
      } else if (type === 1 && slot.emergencyRemaining - selectCont < 0) {
        this.$modal.msgWarning('急诊号余量不足，请选择其他类型的号');
        return;
      } else if (type === 2 && slot.reserveRemaining - selectCont < 0) {
        this.$modal.msgWarning('保留号余量不足，请选择其他类型的号');
        return;
      }
      // 设置预约类型
      this.reservationForm.planType = type;

      // 如果这个时间段还没有被选中，则选中它
      if (!slot.selected) {
        this.selectTimeSlot(slot);
      }

    },

    /** 选择设备 */
    selectDevice(deviceId) {
      this.reservationForm.deviceId = deviceId;
      this.handleDeviceChange();
    },

    /** 格式化日期显示 */
    formatDate(dateStr) {
      if (!dateStr) return '';
      const date = new Date(dateStr);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      return `${year}年${month}月${day}日`;
    },

    /** 获取星期几 */
    getWeekDay(dateStr) {
      if (!dateStr) return '';

      const date = new Date(dateStr);
      const weekDays = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六'];

      return weekDays[date.getDay()];
    },

    /** 获取选中的设备名称 */
    getSelectedDeviceName() {
      if (!this.reservationForm.deviceId) return '';
      const device = this.deviceOptions.find(item => item.deviceId === this.reservationForm.deviceId);
      return device ? device.deviceName : '';
    },
    /** 提交预约 */
    submitReservation() {
      this.$refs.reservationForm.validate(valid => {
        if (!valid) {
          return;
        }

        if (!this.reservationForm.timeSlot) {
          this.$message.warning("请选择预约时间段");
          return;
        }

        // 检查患者ID是否为空
        if (!this.reservationForm.hisPatientId) {
          this.$message.warning("患者编号不能为空");
          return;
        }

        // 检查是否有选中的检查项目
        if (this.selectedExams.length === 0) {
          this.$message.warning("请先选择要预约的检查项目");
          return;
        }

        // 构造批量预约数据
        const requestIds = this.selectedExams.map(exam => exam.requestId);
        const data = {
          requestIds: requestIds,
          planId: this.reservationForm.timeSlot.planId,
          planType: this.reservationForm.planType
        };

        this.loading = true;
        batchSubmitAppointment(data).then(response => {
          if (response.code === 200) {
            this.$modal.msgSuccess(`预约成功，共预约 ${requestIds.length} 个检查项目`);

            // 更新已预约的检查状态
            this.selectedExams.forEach(exam => {
              exam.status = 1;
            });

            // 检查是否所有检查都已预约
            this.checkAllExamsReserved();

            // 重置表单部分字段，但保留患者信息
            this.resetReservationForm(true);

            // 更新可选时间段列表
            this.getAvailableTimeSlots();

            // 更新列表
            this.getList();
          } else {
            this.$modal.msgError("预约失败：" + response.msg);
          }
          this.loading = false;
        }).catch(error => {
          console.error("批量预约失败:", error);
          this.$modal.msgError("预约失败，请稍后重试");
          this.loading = false;
        });
      });
    },

    /** 检查是否所有检查都已预约 */
    checkAllExamsReserved() {
      // 如果所有检查都已预约，关闭预约页面
      const allReserved = this.patientExams.every(exam => exam.status === 1);
      if (allReserved) {
        this.$modal.msgSuccess("所有检查已全部预约完成");
        this.reservationOpen = false;
      }
    },

    /** 重置预约表单 */
    resetReservationForm(partial = false) {
      if (partial) {
        // 只重置检查相关信息，保留时间和设备设置
        Object.assign(this.reservationForm, {
          requestId: "",
          studyExamname: "",
          studyBodypart: "",
          studyBodypartCode: "",
          bodyInfo: null,
          numberSource: ""
        });
      } else {
        // 完全重置
        this.reservationForm = {
          requestId: "",
          hisPatientId: "",
          patientName: "",
          patientTelephone: "",
          patientSex: "",
          studyExamname: "",
          studyPerformdept: "",
          reservationDate: "",
          deptId: "",
          deviceId: "",
          timeSlot: null,
          planType: 0
        };
        this.availableTimeSlots = [];
        this.patientExams = [];
        this.resetSelectionState();
      }
      this.loading = false;
    },

    /** 取消预约对话框 */
    cancelReservation() {
      this.reservationOpen = false;
      this.resetReservationForm();
    },

    /** 取消预约操作 */
    handleCancelAppointment(row) {
      this.$modal.confirm('确认要取消该预约吗？').then(() => {
        this.loading = true;
        // 需要先获取预约ID
        listReservation({ requestId: row.requestId }).then(response => {
          if (response.rows && response.rows.length > 0) {
            const appointmentId = response.rows[0].appointmentId;
            return cancelReservation(appointmentId);
          } else {
            this.$modal.msgError('未找到相关预约信息');
            this.loading = false;
            return Promise.reject();
          }
        }).then(() => {
          this.$modal.msgSuccess('取消预约成功');
          this.getList();
          this.loading = false;
        }).catch(() => {
          this.loading = false;
        });
      });
    },

    /** 查看预约操作 */
    handleViewAppointment(row) {
      this.loading = true;
      listReservation({ requestId: row.requestId }).then(response => {
        if (response.rows && response.rows.length > 0) {
          const appointment = response.rows[0];
          this.$modal.msgInfo(
            `<div style="text-align: left;">
              <p><strong>预约信息：</strong></p>
              <p><strong>患者姓名：</strong>${appointment.patientName}</p>
              <p><strong>患者编号：</strong>${appointment.hisPatientId}</p>
              <p><strong>设备名称：</strong>${appointment.deviceName}</p>
              <p><strong>预约日期：</strong>${appointment.appointmentDate}</p>
              <p><strong>预约时间：</strong>${appointment.appointmentTime}</p>
              <p><strong>预约类型：</strong>${appointment.planType === 0 ? '普通号' : appointment.planType === 1 ? '急诊号' : '保留号'}</p>
              <p><strong>状态：</strong>${appointment.status === 0 ? '待检查' : appointment.status === 1 ? '已检查' : '已取消'}</p>
            </div>`
          );
        } else {
          this.$modal.msgError('未找到相关预约信息');
        }
        this.loading = false;
      }).catch(() => {
        this.loading = false;
      });
    },

    /** 选择患者检查部位 */
    selectExam(exam) {
      console.log("选择检查部位：", exam);
      // 如果检查已预约，提示并退出
      if (exam.status === 1) {
        this.$message.info("该检查已预约");
        return;
      }
      // 保存当前选中的检查信息
      this.reservationForm.requestId = exam.requestId;
      this.reservationForm.studyExamname = exam.studyExamname;
      // 优先使用字典标签作为检查部位名称，如果没有则使用原始的检查部位名称
      this.reservationForm.studyBodypart = exam.bodyInfo && exam.bodyInfo.bodyPartDictLabel || exam.studyBodypart || '';
      // 保存检查部位代码和字典数据，以便后续使用
      if (exam.studyBodypartCode) {
        this.reservationForm.studyBodypartCode = exam.studyBodypartCode;
        // 如果有 bodyInfo 对象，保存其中的数据
        if (exam.bodyInfo) {
          this.reservationForm.bodyInfo = exam.bodyInfo;
          this.reservationForm.numberSource = exam.bodyInfo.numberSource;
        }
      }

      // 检查执行科室是否变更
      const currentDept = this.reservationForm.studyPerformdept;
      const newDept = exam.studyPerformdept || '';
      // 如果执行科室发生变化且新检查有执行科室信息，才更新科室和设备
      if (currentDept !== newDept && exam.studyPerformdeptid) {
        // 设置默认科室，但不清空已选时间和设备
        this.setDefaultDept(exam.studyPerformdeptid, false);
      }

      // 如果该检查有保存的时间段信息，则选中对应的时间段
      if (exam.timeSlot) {
        console.log("选择的时间段：" + exam.timeSlot.planId);
        // 保存时间段到表单
        this.reservationForm.timeSlot = exam.timeSlot;
        // 更新UI中的时间段选中状态
        if (this.availableTimeSlots && this.availableTimeSlots.length > 0) {
          // 查找匹配的时间段并设置为选中状态
          const matchedSlot = this.availableTimeSlots.find(slot =>
            slot.planId === exam.timeSlot.planId
          );
          if (matchedSlot) {
            // 先取消所有时间段的选中状态
            this.availableTimeSlots.forEach(slot => {
              slot.selected = false;
            });
            // 先设置预约类型，再选择时间段
            if (exam.selectedPlanType !== undefined) {
              this.reservationForm.planType = exam.selectedPlanType;
            } else if (exam.timeSlot && exam.timeSlot.planType !== undefined) {
              this.reservationForm.planType = exam.timeSlot.planType;
            }

            console.log("找到匹配的时间段并设置为选中状态");
            // 使用 selectTimeSlot 方法选中时间段，它会同时处理 UI 更新和数据保存
            this.selectTimeSlot(matchedSlot);

          } else {
            console.log("未找到匹配的时间段，可能需要重新加载时间段列表");
          }
        } else {
          console.log("时间段列表为空，需要先加载时间段列表");
        }

        // 如果有保存的预约类型，也恢复
        if (exam.selectedPlanType !== undefined) {
          this.reservationForm.planType = exam.selectedPlanType;
          console.log("恢复预约类型：", exam.selectedPlanType);
        } else if (exam.timeSlot && exam.timeSlot.planType !== undefined) {
          // 如果时间段对象中有预约类型信息，也使用它
          this.reservationForm.planType = exam.timeSlot.planType;
          console.log("从时间段恢复预约类型：", exam.timeSlot.planType);
        } else {
          // 如果没有保存的预约类型，重置为默认值
          this.reservationForm.planType = 0;
          console.log("重置预约类型为默认值：0");
        }
      } else {
        // 清空已选时间段
        this.reservationForm.timeSlot = null;
        // 重置预约类型为默认值
        this.reservationForm.planType = 0; // 默认为普通号
        // 取消所有时间段的选中状态
        if (this.availableTimeSlots && this.availableTimeSlots.length > 0) {
          this.availableTimeSlots.forEach(slot => {
            slot.selected = false;
          });
          // 自动选择普通号可用的时间段
          this.autoSelectAvailableOrdinarySlot();
        }
        console.log("该检查没有保存的时间段信息，已清空已选时间段并尝试自动选择");
      }
    },



    /** 科室变更事件但不重置已选设备和时间段 */
    async handleDeptChangeWithoutReset(deptId) {
      // 获取该科室下的设备列表
      if (deptId) {
        await this.getDeviceList(deptId);
        // handleDeviceChange是同步方法，不需要await
        this.handleDeviceChange();
      } else {
        this.deviceOptions = [];
      }
    },
  }
};
</script>

<style scoped>
/* 预约对话框样式 */
.reservation-container {
  height: 70%;
  overflow: hidden;
}

/* 左侧面板样式 */
.left-panel {
  border-right: 1px solid #ebeef5;
  height: 100%;
  overflow-y: auto;
  padding-right: 15px;
}

/* 患者信息样式 */
.patient-info {
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 10px;
  margin-bottom: 15px;
}

.info-item {
  margin-bottom: 8px;
  font-size: 14px;
  line-height: 1.5;
}

.info-item .label {
  color: #606266;
  font-weight: bold;
}

.info-item .value {
  color: #303133;
}

/* 设备列表样式 */
.section-title {
  font-size: 16px;
  font-weight: bold;
  padding-bottom: 8px;
  border-bottom: 1px solid #ebeef5;
  color: #303133;
}

.title-with-type {
  display: flex;
  align-items: center;
  gap: 15px;
}

.current-plan-type {
  display: flex;
  align-items: center;
  gap: 8px;
}

.plan-type-label {
  font-size: 14px;
  color: #606266;
  font-weight: normal;
}

.device-list-container {
  margin-top: 20px;
}

.device-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.device-list li {
  padding: 10px;
  cursor: pointer;
  border-radius: 4px;
  margin-bottom: 5px;
  transition: all 0.3s;
  display: flex;
  align-items: center;
}

.device-list li:hover {
  background-color: #f5f7fa;
}

.device-list li.active {
  background-color: #ecf5ff;
  color: #409EFF;
  font-weight: bold;
}

.device-list li i {
  margin-right: 8px;
  color: #409EFF;
}

.no-devices {
  text-align: center;
  padding: 20px 0;
  color: #909399;
}

.no-devices i {
  font-size: 24px;
  margin-bottom: 10px;
  display: block;
}

/* 右侧面板样式 */
.right-panel {
  height: 100%;
  overflow-y: auto;
  padding-left: 15px;
}

/* 日期头部样式 */
.date-header {
  color: white;
  border-radius: 4px;
}

.current-date {
  font-size: 18px;
  font-weight: bold;
}

.week-day {
  margin-left: 10px;
  font-size: 16px;
}

/* 排班信息样式 */
.schedule-container {
  min-height: 400px;
}

.no-device-selected,
.no-date-selected,
.no-schedules {
  text-align: center;
  padding: 100px 0;
  color: #909399;
}

.no-device-selected i,
.no-date-selected i,
.no-schedules i {
  font-size: 48px;
  margin-bottom: 15px;
  display: block;
}


.time-slots {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
  gap: 15px;
  margin-top: 15px;
}

.time-slots .el-card {
  cursor: pointer;
  transition: all 0.3s;
}

.time-slots .el-card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.time-slots .el-card.selected-slot {
  border-color: #409EFF;
  background-color: #ecf5ff;
}

.time-slots .el-card.disabled-slot {
  cursor: not-allowed;
  opacity: 0.6;
}

.time-range {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #303133;
}

.time-display {
  margin-bottom: 5px;
}

.disabled-reason {
  display: inline-block;
  font-size: 12px;
  color: #F56C6C;
  background-color: #FEF0F0;
  padding: 2px 6px;
  border-radius: 2px;
  font-weight: normal;
  text-align: center;
  width: 100%;
}

.disabled-reason-text {
  display: inline-block;
  width: 100%;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: help;
}

.remaining-counts {
  display: flex;
  flex-direction: column;
  gap: 5px;
  margin-top: 10px;
}

.remaining-count-item {
  display: flex;
  justify-content: space-between;
}

.remaining-count-item .el-tag {
  width: 100%;
  display: flex;
  justify-content: space-between;
  padding: 0 5px;
}

.remaining-count-item .count {
  font-weight: bold;
}

.remaining-count-item.total {
  margin-top: 5px;
}

.remaining-count-item .el-tag.selected-type {
  font-weight: bold;
  transform: scale(1.05);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.12);
}

.remaining-count-item .el-tag:hover {
  cursor: pointer;
  transform: scale(1.02);
  transition: all 0.2s;
}

.remaining-count-item .el-tag.disabled-tag {
  cursor: not-allowed;
  opacity: 0.6;
}

.remaining-count-item .el-tag.disabled-tag:hover {
  cursor: not-allowed;
  transform: none;
}

/* 验证错误信息样式 */
.validation-error {
  font-size: 11px;
  color: #F56C6C;
  background-color: #FEF0F0;
  padding: 2px 4px;
  border-radius: 2px;
  margin-top: 2px;
  text-align: center;
  line-height: 1.2;
}



/* 操作按钮样式 */
.operation-buttons {
  display: flex;
  flex-direction: column;
  align-items: center;
}

.operation-buttons .el-button {
  margin: 1px 0;
  padding: 2px 0;
}

.operation-buttons .el-button+.el-button {
  margin-left: 0;
}

/* 固定列样式 */
.el-table .fixed-right {
  box-shadow: -2px 0 8px rgba(0, 0, 0, 0.1);
  background-color: #fff;
}

/* 表格横向滚动样式 */
.el-table {
  overflow-x: auto;
}

/* 患者检查卡片区域样式 */
.patient-exams-container {
  margin: 20px 0;
}

.exams-scroll-container {
  overflow-x: auto;
  padding: 10px 0;
}

.exams-cards {
  display: flex;
  flex-wrap: nowrap;
  gap: 15px;
  padding-bottom: 10px;
}

.exam-card {
  min-width: 250px;
  max-width: 300px;
  flex: 0 0 auto;
  transition: all 0.3s;
}

.exam-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 6px 12px rgba(0, 0, 0, 0.1);
}

.exam-title {
  font-size: 16px;
  font-weight: bold;
  color: #303133;
  margin-bottom: 12px;
  border-left: 3px solid #409EFF;
  padding-left: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.exam-title .title-row {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.exam-title .title-row:last-child {
  margin-bottom: 0;
}

.exam-title .dept-label,
.exam-title .part-label {
  color: #606266;
  margin-right: 4px;
  flex-shrink: 0;
}

.exam-title .dept-value,
.exam-title .part-value {
  color: #409EFF;
  font-weight: bold;
  overflow: hidden;
  text-overflow: ellipsis;
}

.exam-info {
  margin-bottom: 10px;
}

.exam-info p {
  margin: 8px 0;
  font-size: 14px;
  display: flex;
  align-items: center;
}

.exam-info i {
  margin-right: 5px;
  color: #909399;
}

.exam-status {
  text-align: center;
  padding: 6px 0;
  background-color: #f5f7fa;
  border-radius: 4px;
  font-weight: bold;
  color: #909399;
}

.exam-status.status-reserved {
  background-color: #f0f9eb;
  color: #67c23a;
}



/* 复选框样式 */
.exam-checkbox {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
  padding: 8px;
  pointer-events: none;
  /* 禁用鼠标事件 */
  border-radius: 4px;
}

/* 移除复选框的悬停效果，因为现在是只读的 */

.exam-card-wrapper {
  cursor: pointer;
  transition: all 0.3s ease;
}

.exam-card-wrapper:hover .exam-card {
  transform: translateY(-2px);
  box-shadow: 0 6px 16px rgba(0, 0, 0, 0.15);
}

.exam-card-wrapper:active .exam-card {
  transform: translateY(0);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.exam-card {
  position: relative;
  transition: all 0.3s ease;
}

/* 已预约的卡片样式 */
.exam-card-wrapper.status-reserved-wrapper {
  cursor: not-allowed;
}

.exam-card-wrapper.status-reserved-wrapper:hover .exam-card {
  transform: none;
  box-shadow: none;
}

.exam-card.status-reserved {
  opacity: 0.7;
  background-color: #f5f7fa;
}

/* 选中状态的卡片样式 */
.exam-card.checked-exam {
  border: 2px solid #67c23a;
  box-shadow: 0 4px 12px rgba(103, 194, 58, 0.3);
  background-color: #f0f9eb;
}

.exam-card.checked-exam .exam-title {
  border-left-color: #67c23a;
}

/* 标题区域样式 */
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
  padding: 15px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  border-radius: 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.title-left {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.main-title {
  font-size: 18px;
  font-weight: bold;
  color: #303133;
}

.exam-statistics {
  display: flex;
  gap: 20px;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  background-color: rgba(255, 255, 255, 0.8);
  border-radius: 20px;
  font-size: 14px;
  color: #606266;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  transition: all 0.3s;
}

.stat-item:hover {
  background-color: rgba(255, 255, 255, 0.95);
  transform: translateY(-1px);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
}

.stat-item i {
  color: #409EFF;
  font-size: 16px;
}

.stat-item-placeholder {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 6px 12px;
  background-color: rgba(255, 255, 255, 0.6);
  border-radius: 20px;
  font-size: 14px;
  color: #909399;
  font-style: italic;
}

.stat-item-placeholder i {
  color: #C0C4CC;
  font-size: 16px;
}

/* 选择控制区域样式 */
.selection-controls {
  display: flex;
  align-items: center;
  gap: 15px;
  flex-wrap: wrap;
}

.selected-info {
  color: #409EFF;
  font-weight: bold;
  font-size: 14px;
  padding: 6px 12px;
  background-color: rgba(64, 158, 255, 0.1);
  border-radius: 15px;
  border: 1px solid rgba(64, 158, 255, 0.3);
}

/* 批量预约按钮样式 */
.dialog-footer .el-button--success {
  background-color: #67c23a;
  border-color: #67c23a;
}

.dialog-footer .el-button--success:hover {
  background-color: #85ce61;
  border-color: #85ce61;
}
</style>
