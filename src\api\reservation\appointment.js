import request from '@/utils/request'

// 查询预约列表
export function listAppointment(query) {
  return request({
    url: '/reservation/appointment/list',
    method: 'get',
    params: query
  })
}

// 查询预约详细
export function getAppointment(appointmentId) {
  return request({
    url: '/reservation/appointment/' + appointmentId,
    method: 'get'
  })
}

// 新增预约
export function addAppointment(data) {
  return request({
    url: '/reservation/appointment',
    method: 'post',
    data: data
  })
}

// 修改预约
export function updateAppointment(data) {
  return request({
    url: '/reservation/appointment/cancel',
    method: 'put',
    data: data
  })
}

// 删除预约
export function delAppointment(appointmentId) {
  return request({
    url: '/reservation/appointment/' + appointmentId,
    method: 'delete'
  })
}

// 取消预约
export function cancelAppointment(appointmentId) {
  return request({
    url: '/reservation/appointment/cancel/' + appointmentId,
    method: 'put'
  })
}

// 获取预约数量
export function getAppointmentCount(query) {
  return request({
    url: '/reservation/appointment/count',
    method: 'get',
    params: query
  })
}

// 自动预约
export function autoSubmitAppointment(studyRequestIds) {
  return request({
    url: '/reservation/appointment/autoSubmit',
    method: 'post',
    data: { studyRequestIds }
  })
}

// 验证预约时间段是否可用
export function validateAppointment(data) {
  return request({
    url: '/reservation/appointment/validate',
    method: 'post',
    data: data
  })
}

// 批量提交预约
export function batchSubmitAppointment(data) {
  return request({
    url: '/reservation/appointment/batchSubmit',
    method: 'post',
    data: data
  })
}
