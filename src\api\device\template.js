import request from '@/utils/request'

// 查询排班模板列表
export function listTemplate(query) {
  return request({
    url: '/device/template/list',
    method: 'get',
    params: query
  })
}

// 查询排班模板详细
export function getTemplate(templateId) {
  return request({
    url: '/device/template/' + templateId,
    method: 'get'
  })
}

// 新增排班模板
export function addTemplate(data) {
  return request({
    url: '/device/template',
    method: 'post',
    data: data
  })
}

// 修改排班模板
export function updateTemplate(data) {
  return request({
    url: '/device/template',
    method: 'put',
    data: data
  })
}

// 删除排班模板
export function delTemplate(templateId) {
  return request({
    url: '/device/template/' + templateId,
    method: 'delete'
  })
}
