import request from '@/utils/request'

// 查询预约详情
export function getReservation(reservationId) {
  return request({
    url: '/reservation/reservation/' + reservationId,
    method: 'get'
  })
}

// 查询预约列表
export function listReservation(query) {
  return request({
    url: '/reservation/reservation/list',
    method: 'get',
    params: query
  })
}

// 新增预约
export function addReservation(data) {
  return request({
    url: '/reservation/appointment/submit',
    method: 'post',
    data: data
  })
}

// 修改预约
export function updateReservation(data) {
  return request({
    url: '/reservation/reservation',
    method: 'put',
    data: data
  })
}

// 删除预约
export function delReservation(reservationId) {
  return request({
    url: '/reservation/reservation/' + reservationId,
    method: 'delete'
  })
}

// 取消预约
export function cancelReservation(reservationId) {
  return request({
    url: '/reservation/reservation/cancel/' + reservationId,
    method: 'put'
  })
}
