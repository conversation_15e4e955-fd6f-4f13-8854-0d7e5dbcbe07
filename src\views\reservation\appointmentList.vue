<!-- 已预约查询 -->
<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="患者姓名" prop="patientName">
        <el-input
          v-model="queryParams.patientName"
          placeholder="请输入患者姓名"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="患者编号" prop="hisPatientId">
        <el-input
          v-model="queryParams.hisPatientId"
          placeholder="请输入患者编号"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="预约日期" prop="appointmentDate">
        <el-date-picker
          v-model="dateRange"
          style="width: 240px"
          value-format="yyyy-MM-dd"
          type="daterange"
          range-separator="-"
          start-placeholder="开始日期"
          end-placeholder="结束日期"
        ></el-date-picker>
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="预约状态" clearable>
          <el-option
            v-for="dict in statusOptions"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['reservation:appointment:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['reservation:appointment:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['reservation:appointment:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['reservation:appointment:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="appointmentList" @selection-change="handleSelectionChange" style="width: 100%;" :header-cell-style="{backgroundColor: '#fafafa'}" border>
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="预约ID" align="center" prop="appointmentId" width="80" />
      <el-table-column label="患者姓名" align="center" prop="patientName" width="100" />
      <el-table-column label="患者编号" align="center" prop="hisPatientId" min-width="120" :show-overflow-tooltip="true" />
      <el-table-column label="性别" align="center" prop="patientSex" width="60">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_user_sex" :value="scope.row.patientSex"/>
        </template>
      </el-table-column>
      <el-table-column label="联系电话" align="center" prop="patientPhone" width="120" />
      <el-table-column label="设备名称" align="center" min-width="120" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ scope.row.device ? scope.row.device.deviceName : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="设备地址" align="center" min-width="150" :show-overflow-tooltip="true">
        <template slot-scope="scope">
          <span>{{ scope.row.device ? scope.row.device.deviceLocation : '' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="预约日期" align="center" prop="appointmentDate" width="100" />
      <el-table-column label="预约时间" align="center" prop="appointmentTime" width="120" />
      <el-table-column label="预约类型" align="center" prop="planType" width="80">
        <template slot-scope="scope">
          <el-tag type="success" v-if="scope.row.planType === 0 || scope.row.planType === '0'">普通号</el-tag>
          <el-tag type="danger" v-if="scope.row.planType === 1 || scope.row.planType === '1'">急诊号</el-tag>
          <el-tag type="primary" v-if="scope.row.planType === 2 || scope.row.planType === '2'">保留号</el-tag>
          <span v-if="scope.row.planType === undefined || scope.row.planType === null">{{ scope.row.planType }}</span>
        </template>
      </el-table-column>
      <el-table-column label="状态" align="center" prop="status" width="80">
        <template slot-scope="scope">
          <el-tag type="info" v-if="scope.row.status === 0 || scope.row.status === '0'">待检查</el-tag>
          <el-tag type="success" v-if="scope.row.status === 1 || scope.row.status === '1'">已检查</el-tag>
          <el-tag type="danger" v-if="scope.row.status === 2 || scope.row.status === '2'">已取消</el-tag>
          <span v-if="scope.row.status === undefined || scope.row.status === null">{{ scope.row.status }}</span>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="200">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150" fixed="right">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-view"
            @click="handleDetail(scope.row)"
            v-hasPermi="['reservation:appointment:query']"
          >详情</el-button>
          <el-button
            v-if="scope.row.status === 0 || scope.row.status === '0'"
            size="mini"
            type="text"
            icon="el-icon-close"
            @click="handleCancel(scope.row)"
            v-hasPermi="['reservation:appointment:edit']"
          >取消预约</el-button>

        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 预约详情对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="500px" append-to-body>
      <el-form ref="form" :model="form" label-width="100px">
        <el-row>
          <el-col :span="24">
            <el-form-item label="患者姓名">
              <el-input v-model="form.patientName" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="患者编号">
              <el-input v-model="form.hisPatientId" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="联系电话">
              <el-input v-model="form.patientPhone" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="设备名称">
              <el-input :value="getDeviceName()" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="设备地址">
              <el-input :value="getDeviceLocation()" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预约日期">
              <el-input v-model="form.appointmentDate" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预约时间">
              <el-input v-model="form.appointmentTime" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="预约类型">
              <el-tag type="success" v-if="form.planType === 0 || form.planType === '0'">普通号</el-tag>
              <el-tag type="danger" v-if="form.planType === 1 || form.planType === '1'">急诊号</el-tag>
              <el-tag type="primary" v-if="form.planType === 2 || form.planType === '2'">保留号</el-tag>
              <span v-if="form.planType === undefined || form.planType === null">{{ form.planType }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="状态">
              <el-tag type="info" v-if="form.status === 0 || form.status === '0'">待检查</el-tag>
              <el-tag type="success" v-if="form.status === 1 || form.status === '1'">已检查</el-tag>
              <el-tag type="danger" v-if="form.status === 2 || form.status === '2'">已取消</el-tag>
              <span v-if="form.status === undefined || form.status === null">{{ form.status }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24" v-if="form.status === 2 || form.status === '2'">
            <el-form-item label="取消原因">
              <el-input v-model="form.cancelReason" type="textarea" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="open = false">关 闭</el-button>
      </div>
    </el-dialog>

    <!-- 取消预约对话框 -->
    <el-dialog title="取消预约" :visible.sync="cancelOpen" width="500px" append-to-body>
      <el-form ref="cancelForm" :model="cancelForm" :rules="cancelRules" label-width="100px">
        <el-form-item label="取消原因" prop="cancelReason">
          <el-input v-model="cancelForm.cancelReason" type="textarea" placeholder="请输入取消原因" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitCancel">确 定</el-button>
        <el-button @click="cancelOpen = false">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listAppointment, getAppointment, updateAppointment, delAppointment } from "@/api/reservation/appointment";
import { parseTime } from "@/utils/ruoyi";

export default {
  name: "AppointmentList",
  dicts: ['sys_user_sex'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 预约表格数据
      appointmentList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示取消预约弹出层
      cancelOpen: false,
      // 日期范围
      dateRange: [],
      // 状态选项
      statusOptions: [
        { value: 0, label: '待检查' },
        { value: 1, label: '已检查' },
        { value: 2, label: '已取消' }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        patientName: undefined,
        hisPatientId: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 取消预约表单
      cancelForm: {
        appointmentId: null,
        cancelReason: ""
      },
      // 取消预约表单校验规则
      cancelRules: {
        cancelReason: [
          { required: true, message: "取消原因不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询预约列表 */
    getList() {
      this.loading = true;
      listAppointment(this.addDateRange(this.queryParams, this.dateRange, 'appointmentDate')).then(response => {
        this.appointmentList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {};
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.dateRange = [];
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.appointmentId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      // 使用 router.push 而不是直接跳转，保持导航栏和侧边栏
      this.$router.push({ path: '/reservation/studyRequest' });
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const appointmentId = row.appointmentId || this.ids;
      getAppointment(appointmentId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改预约";
      });
    },
    /** 详情按钮操作 */
    handleDetail(row) {
      this.reset();
      const appointmentId = row.appointmentId || this.ids;
      // getAppointment(appointmentId).then(response => {
      //   this.form = response.data;
      //   this.open = true;
      //   this.title = "预约详情";
      // });
      this.form = row

      this.open = true;
      this.title = "预约详情";
    },

    /** 取消预约按钮操作 */
    handleCancel(row) {
      this.cancelForm = {
        appointmentId: row.appointmentId,
        cancelReason: ""
      };
      this.cancelOpen = true;
    },

    /** 提交取消预约 */
    submitCancel() {
      this.$refs["cancelForm"].validate(valid => {
        if (valid) {
          this.loading = true;
          // 更新预约状态为已取消
          const data = {
            appointmentId: this.cancelForm.appointmentId,
            status: 2, // 已取消
            cancelReason: this.cancelForm.cancelReason
          };
          updateAppointment(data).then(() => {
            this.$modal.msgSuccess("取消预约成功");
            this.cancelOpen = false;
            this.getList();
            this.loading = false;
          }).catch(() => {
            this.loading = false;
          });
        }
      });
    },

    /** 删除按钮操作 */
    handleDelete(row) {
      const appointmentIds = row.appointmentId || this.ids;
      this.$modal.confirm('是否确认删除预约编号为"' + appointmentIds + '"的数据项？').then(() => {
        this.loading = true;
        return delAppointment(appointmentIds);
      }).then(() => {
        this.loading = false;
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {
        this.loading = false;
      });
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('reservation/appointment/export', {
        ...this.queryParams
      }, `appointment_${new Date().getTime()}.xlsx`);
    },

    /** 获取设备名称 */
    getDeviceName() {
      return this.form.device ? this.form.device.deviceName : '';
    },

    /** 获取设备地址 */
    getDeviceLocation() {
      return this.form.device ? this.form.device.deviceLocation : '';
    }
  }
};
</script>

<style scoped>
.el-tag {
  margin-right: 5px;
}

.app-container {
  padding: 20px;
  height: 100%;
  width: 100%;
}

.el-table {
  margin-top: 15px;
}
</style>
