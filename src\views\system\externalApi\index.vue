<template>
  <div class="app-container">
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
      <el-form-item label="接口名称" prop="apiName">
        <el-input
          v-model="queryParams.apiName"
          placeholder="请输入接口名称"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="接口代码" prop="apiCode">
        <el-input
          v-model="queryParams.apiCode"
          placeholder="请输入接口代码"
          clearable
          style="width: 240px"
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="状态" prop="status">
        <el-select v-model="queryParams.status" placeholder="接口状态" clearable style="width: 240px">
          <el-option
            v-for="dict in dict.type.sys_normal_disable"
            :key="dict.value"
            :label="dict.label"
            :value="dict.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <el-row :gutter="10" class="mb8">
      <el-col :span="1.5">
        <el-button
          type="primary"
          plain
          icon="el-icon-plus"
          size="mini"
          @click="handleAdd"
          v-hasPermi="['system:api:add']"
        >新增</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="success"
          plain
          icon="el-icon-edit"
          size="mini"
          :disabled="single"
          @click="handleUpdate"
          v-hasPermi="['system:api:edit']"
        >修改</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="danger"
          plain
          icon="el-icon-delete"
          size="mini"
          :disabled="multiple"
          @click="handleDelete"
          v-hasPermi="['system:api:remove']"
        >删除</el-button>
      </el-col>
      <el-col :span="1.5">
        <el-button
          type="warning"
          plain
          icon="el-icon-download"
          size="mini"
          @click="handleExport"
          v-hasPermi="['system:api:export']"
        >导出</el-button>
      </el-col>
      <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
    </el-row>

    <el-table v-loading="loading" :data="apiList" @selection-change="handleSelectionChange">
      <el-table-column type="selection" width="55" align="center" />
      <el-table-column label="接口ID" align="center" prop="apiId" />
      <el-table-column label="接口名称" align="center" prop="apiName" :show-overflow-tooltip="true" />
      <el-table-column label="接口代码" align="center" prop="apiCode" :show-overflow-tooltip="true" />
      <el-table-column label="接口URL" align="center" prop="apiUrl" :show-overflow-tooltip="true" />
      <el-table-column label="请求方法" align="center" prop="requestMethod" />
      <el-table-column label="超时时间(毫秒)" align="center" prop="timeout" />
      <el-table-column label="状态" align="center" prop="status">
        <template slot-scope="scope">
          <dict-tag :options="dict.type.sys_normal_disable" :value="scope.row.status"/>
        </template>
      </el-table-column>
      <el-table-column label="创建时间" align="center" prop="createTime" width="180">
        <template slot-scope="scope">
          <span>{{ parseTime(scope.row.createTime) }}</span>
        </template>
      </el-table-column>
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <el-button
            size="mini"
            type="text"
            icon="el-icon-edit"
            @click="handleUpdate(scope.row)"
            v-hasPermi="['system:api:edit']"
          >修改</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-delete"
            @click="handleDelete(scope.row)"
            v-hasPermi="['system:api:remove']"
          >删除</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-connection"
            @click="handleTest(scope.row)"
            v-hasPermi="['system:api:test']"
          >测试</el-button>
          <el-button
            size="mini"
            type="text"
            icon="el-icon-notebook-2"
            @click="handleViewLogs(scope.row)"
            v-hasPermi="['system:apilog:list']"
          >日志</el-button>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getList"
    />

    <!-- 添加或修改外部接口对话框 -->
    <el-dialog :title="title" :visible.sync="open" width="700px" append-to-body :close-on-click-modal="false">
      <el-form ref="form" :model="form" :rules="rules" label-width="120px" >
        <el-row>
          <el-col :span="12">
            <el-form-item label="接口名称" prop="apiName">
              <el-input v-model="form.apiName" placeholder="请输入接口名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="接口代码" prop="apiCode">
              <el-input v-model="form.apiCode" placeholder="请输入接口代码" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="接口URL" prop="apiUrl">
          <el-input v-model="form.apiUrl" type="textarea" :rows="3" placeholder="请输入接口URL" />
        </el-form-item>
        <el-row>
          <el-col :span="12">
            <el-form-item label="请求方法" prop="requestMethod">
              <el-select v-model="form.requestMethod" placeholder="请选择请求方法">
                <el-option label="GET" value="GET" />
                <el-option label="POST" value="POST" />
                <el-option label="PUT" value="PUT" />
                <el-option label="DELETE" value="DELETE" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="内容类型" prop="contentType">
              <el-input v-model="form.contentType" placeholder="请输入内容类型" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="超时时间" prop="timeout">
              <el-input-number v-model="form.timeout" :min="1000" :step="1000" controls-position="right" placeholder="超时时间(毫秒)" style="width: 100%" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="重试次数" prop="retryCount">
              <el-input-number v-model="form.retryCount" :min="0" :max="10" controls-position="right" placeholder="重试次数" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="12">
            <el-form-item label="重试间隔" prop="retryInterval">
              <el-input-number v-model="form.retryInterval" :min="100" :step="100" controls-position="right" placeholder="重试间隔(毫秒)" style="width: 100%" />
            </el-form-item>
          </el-col>
        </el-row>
        <el-form-item label="状态" prop="status">
          <el-radio-group v-model="form.status">
            <el-radio
              v-for="dict in dict.type.sys_normal_disable"
              :key="dict.value"
              :label="dict.value"
            >{{dict.label}}</el-radio>
          </el-radio-group>
        </el-form-item>
        <el-form-item label="备注" prop="remark">
          <el-input v-model="form.remark" type="textarea" placeholder="请输入内容" />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitForm">确 定</el-button>
        <el-button @click="cancel">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 测试接口对话框 -->
    <el-dialog title="测试接口连接" :visible.sync="testOpen" width="600px" append-to-body :close-on-click-modal="false">
      <el-form ref="testForm" :model="testForm" label-width="100px">
        <el-form-item label="接口URL" prop="apiUrl">
          <el-input v-model="testForm.apiUrl" type="textarea" :rows="3" placeholder="请输入接口URL" />
        </el-form-item>
        <el-form-item label="测试结果">
          <el-input v-model="testResult" type="textarea" :rows="8" readonly />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitTest" :loading="testLoading">测 试</el-button>
        <el-button @click="testOpen = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listExternalApi, getExternalApi, delExternalApi, addExternalApi, updateExternalApi, testExternalApiConnection } from "@/api/system/externalApi";

export default {
  name: "ExternalApi",
  dicts: ['sys_normal_disable'],
  data() {
    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 外部接口表格数据
      apiList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 是否显示测试弹出层
      testOpen: false,
      // 测试加载状态
      testLoading: false,
      // 测试结果
      testResult: "",
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        apiName: undefined,
        apiCode: undefined,
        status: undefined
      },
      // 表单参数
      form: {},
      // 测试表单参数
      testForm: {},
      // 表单校验
      rules: {
        apiName: [
          { required: true, message: "接口名称不能为空", trigger: "blur" }
        ],
        apiCode: [
          { required: true, message: "接口代码不能为空", trigger: "blur" }
        ],
        apiUrl: [
          { required: true, message: "接口URL不能为空", trigger: "blur" }
        ],
        requestMethod: [
          { required: true, message: "请求方法不能为空", trigger: "change" }
        ],
        contentType: [
          { required: true, message: "内容类型不能为空", trigger: "blur" }
        ],
        timeout: [
          { required: true, message: "超时时间不能为空", trigger: "blur" }
        ]
      }
    };
  },
  created() {
    this.getList();
  },
  methods: {
    /** 查询外部接口列表 */
    getList() {
      this.loading = true;
      listExternalApi(this.queryParams).then(response => {
        this.apiList = response.rows;
        this.total = response.total;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        apiId: undefined,
        apiName: undefined,
        apiCode: undefined,
        apiUrl: undefined,
        requestMethod: "POST",
        contentType: "application/json",
        timeout: 5000,
        retryCount: 3,
        retryInterval: 1000,
        status: "0",
        remark: undefined
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.apiId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      this.open = true;
      this.title = "添加外部接口";
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.reset();
      const apiId = row.apiId || this.ids;
      getExternalApi(apiId).then(response => {
        this.form = response.data;
        this.open = true;
        this.title = "修改外部接口";
      });
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["form"].validate(valid => {
        if (valid) {
          if (this.form.apiId != null) {
            updateExternalApi(this.form).then(() => {
              this.$modal.msgSuccess("修改成功");
              this.open = false;
              this.getList();
            });
          } else {
            addExternalApi(this.form).then(() => {
              this.$modal.msgSuccess("新增成功");
              this.open = false;
              this.getList();
            });
          }
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const apiIds = row.apiId || this.ids;
      this.$modal.confirm('是否确认删除外部接口编号为"' + apiIds + '"的数据项？').then(() => {
        return delExternalApi(apiIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download('system/api/export', {
        ...this.queryParams
      }, `external_api_${new Date().getTime()}.xlsx`);
    },
    /** 测试按钮操作 */
    handleTest(row) {
      this.testForm = {
        apiCode: row.apiCode,
        apiUrl: row.apiUrl
      };
      this.testResult = "";
      this.testOpen = true;
    },
    /** 提交测试 */
    submitTest() {
      this.testLoading = true;
      testExternalApiConnection(this.testForm.apiCode, this.testForm).then(response => {
        this.testResult = response.msg;
        this.testLoading = false;
      }).catch(error => {
        this.testResult = "测试失败: " + error.message;
        this.testLoading = false;
      });
    },
    /** 查看日志按钮操作 */
    handleViewLogs(row) {
      const apiId = row.apiId;
      const apiCode = row.apiCode;
      this.$router.push({
        path: '/system/externalApiLog',
        query: {
          apiId: apiId,
          apiCode: apiCode
        }
      });
    }
  }
};
</script>
