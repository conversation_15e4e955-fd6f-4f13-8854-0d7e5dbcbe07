<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!-- 设备信息卡片 -->
      <el-col :span="24">
        <el-card class="box-card" v-if="deviceInfo">
          <div slot="header" class="clearfix">
            <span>设备信息</span>
            <el-button
              style="float: right; padding: 3px 0"
              type="text"
              icon="el-icon-back"
              @click="goBack"
            >返回规则分配</el-button>
          </div>
          <el-descriptions :column="3" border>
            <el-descriptions-item label="设备名称">{{ deviceInfo.deviceName }}</el-descriptions-item>
            <el-descriptions-item label="设备位置">{{ deviceInfo.deviceLocation }}</el-descriptions-item>
          </el-descriptions>
        </el-card>
      </el-col>

      <!-- 规则列表 -->
      <el-col :span="24" style="margin-top: 20px;">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch"
          label-width="68px">
          <el-form-item label="规则名称" prop="ruleName">
            <el-input v-model="queryParams.ruleName" placeholder="请输入规则名称" clearable style="width: 240px"
              @keyup.enter.native="handleQuery" />
          </el-form-item>
          <el-form-item label="状态" prop="status">
            <el-select v-model="queryParams.status" placeholder="规则状态" clearable style="width: 240px">
              <el-option v-for="dict in statusOptions" :key="dict.value" :label="dict.label" :value="dict.value" />
            </el-select>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button type="primary" plain icon="el-icon-plus" size="mini" @click="handleAssignRule"
              v-hasPermi="['device:rule:assign']">分配规则</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getDeviceRules"></right-toolbar>
        </el-row>

        <el-table v-loading="loading" :data="deviceRuleList">
          <el-table-column label="规则ID" align="center" prop="ruleId" />
          <el-table-column label="规则名称" align="center" prop="ruleName" :show-overflow-tooltip="true" />
          <el-table-column label="规则描述" align="center" prop="ruleDesc" :show-overflow-tooltip="true" />
          <el-table-column label="规则数量" align="center" prop="ruleSize" />
          <el-table-column label="状态" align="center" prop="status">
            <template slot-scope="scope">
              <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
                {{ scope.row.status === '0' ? '正常' : '停用' }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
            <template slot-scope="scope">
              <el-button size="mini" type="text" icon="el-icon-view" @click="handleViewRule(scope.row)"
                v-hasPermi="['device:rule:query']">查看</el-button>
              <el-button size="mini" type="text" icon="el-icon-delete" @click="handleRemoveRule(scope.row)"
                v-hasPermi="['device:rule:remove']">移除</el-button>
            </template>
          </el-table-column>
        </el-table>

        <pagination v-show="total > 0" :total="total" :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize" @pagination="getDeviceRules" />
      </el-col>
    </el-row>

    <!-- 规则选择对话框 -->
    <el-dialog title="选择规则" :visible.sync="ruleDialogVisible" width="800px" append-to-body>
      <el-form :model="ruleQueryParams" ref="ruleQueryForm" size="small" :inline="true" label-width="68px">
        <el-form-item label="规则名称" prop="ruleName">
          <el-input
            v-model="ruleQueryParams.ruleName"
            placeholder="请输入规则名称"
            clearable
            style="width: 240px"
            @keyup.enter.native="handleRuleQuery"
          />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" icon="el-icon-search" size="mini" @click="handleRuleQuery">搜索</el-button>
          <el-button icon="el-icon-refresh" size="mini" @click="resetRuleQuery">重置</el-button>
        </el-form-item>
      </el-form>

      <el-table
        v-loading="ruleLoading"
        :data="allRuleList"
        @selection-change="handleRuleSelectionChange"
        height="380px"
        ref="ruleTable"
      >
        <el-table-column type="selection" width="55" align="center" />
        <el-table-column label="规则ID" align="center" prop="ruleId" />
        <el-table-column label="规则名称" align="center" prop="ruleName" :show-overflow-tooltip="true" />
        <el-table-column label="规则描述" align="center" prop="ruleDesc" :show-overflow-tooltip="true" />
        <el-table-column label="状态" align="center" prop="status">
          <template slot-scope="scope">
            <el-tag :type="scope.row.status === '0' ? 'success' : 'danger'">
              {{ scope.row.status === '0' ? '正常' : '停用' }}
            </el-tag>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        v-show="ruleTotal>0"
        :total="ruleTotal"
        :page.sync="ruleQueryParams.pageNum"
        :limit.sync="ruleQueryParams.pageSize"
        @pagination="getRuleList"
      />

      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="handleAssignRules">确 定</el-button>
        <el-button @click="ruleDialogVisible = false">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 查看规则详情对话框 -->
    <el-dialog :title="'规则详情 - ' + (currentRule.ruleName || '')" :visible.sync="ruleDetailVisible" width="50%" append-to-body>
      <el-descriptions :column="1" border>
        <el-descriptions-item label="规则ID">{{ currentRule.ruleId }}</el-descriptions-item>
        <el-descriptions-item label="规则名称">{{ currentRule.ruleName }}</el-descriptions-item>
        <el-descriptions-item label="规则描述">{{ currentRule.ruleDesc }}</el-descriptions-item>
        <el-descriptions-item label="状态">
          <el-tag :type="currentRule.status === '0' ? 'success' : 'danger'">
            {{ currentRule.status === '0' ? '正常' : '停用' }}
          </el-tag>
        </el-descriptions-item>
      </el-descriptions>

      <el-divider content-position="center">规则条件</el-divider>

      <div v-if="currentRule.conditions && currentRule.conditions.length > 0">
        <div v-for="(condition, index) in currentRule.conditions" :key="index" class="condition-item">
          <el-descriptions :column="2" border>
            <!-- 条件类型 - 固定大小 -->
            <el-descriptions-item label="条件类型" class="fixed-size-item">
              <div class="fixed-content">
                {{ getConditionTypeLabel(condition.conditionType) }}
              </div>
            </el-descriptions-item>

            <!-- 操作符 - 固定大小 -->
            <el-descriptions-item label="操作符" class="fixed-size-item">
              <div class="fixed-content">
                {{ getOperatorLabel(condition.conditionOperator) }}
              </div>
            </el-descriptions-item>

            <!-- 条件值 - 动态大小 -->
            <el-descriptions-item label="条件值" :span="2">
              <!-- 时间条件 -->
              <div v-if="condition.conditionType === 'time' && condition.conditionOperator === 'between'" class="condition-value-content">
                {{ formatTimeRange(condition.conditionValue) }}
              </div>
              <!-- 其他条件 -->
              <div v-else class="condition-value-content">
                <div class="tag-container">
                  <el-tag
                    v-for="tag in getConditionTags(condition.conditionValue, condition.conditionType)"
                    :key="tag.value"
                    style="margin-right: 5px; margin-bottom: 5px;"
                  >
                    {{ tag.label }}
                  </el-tag>
                </div>
              </div>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
      <div v-else style="text-align: center; color: #909399; margin: 20px 0;">
        没有规则条件
      </div>

      <div slot="footer" class="dialog-footer">
        <el-button @click="ruleDetailVisible = false">关 闭</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { listRule, getRule } from "@/api/device/rule";
import { getDeviceRules, assignRules, removeDeviceRule } from "@/api/device/rule";
import { getModality } from "@/api/device/device";
import { listDept } from "@/api/system/dept";
import { listData } from "@/api/system/dict/data";

export default {
  name: "DeviceRuleInfo",
  dicts: ['rule_condition_type', 'study_body_part', 'study_body_part_code', 'study_patient_source'],
  data() {
    return {
      // 遮罩层
      loading: true,
      ruleLoading: true,
      // 设备ID
      deviceId: null,
      // 设备信息
      deviceInfo: null,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备规则列表
      deviceRuleList: [],
      // 状态选项
      statusOptions: [
        { value: "0", label: "正常" },
        { value: "1", label: "停用" }
      ],
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        ruleName: null,
        status: null
      },
      // 规则选择对话框可见性
      ruleDialogVisible: false,
      // 规则详情对话框可见性
      ruleDetailVisible: false,
      // 当前查看的规则
      currentRule: {},
      // 所有规则列表
      allRuleList: [],
      // 选中的规则ID数组
      selectedRuleIds: [],
      // 规则总数
      ruleTotal: 0,
      // 规则查询参数
      ruleQueryParams: {
        pageNum: 1,
        pageSize: 10,
        ruleName: null
      },
      // 部门标签映射
      deptLabelMap: {},
      // 检查部位选项
      bodyPartOptions: [],
      // 患者来源选项
      patientSourceOptions: []
    };
  },
  created() {
    // 从路由参数获取设备ID
    this.deviceId = this.$route.params.deviceId;
    if (!this.deviceId) {
      this.$modal.msgError("设备ID不能为空");
      this.goBack();
      return;
    }

    // 获取设备信息
    this.getDeviceInfo();
    // 获取设备规则列表
    this.getDeviceRules();
    // 获取部门树
    this.getDeptTree();
    // 初始化选项
    this.initBodyPartOptions();
    this.initPatientSourceOptions();
  },
  methods: {
    /** 返回规则分配页面 */
    goBack() {
      // 关闭当前页面并返回规则分配页面
      const obj = { path: "/rule/deviceRuleAssign" };
      this.$tab.closeOpenPage(obj);
    },

    /** 获取设备信息 */
    getDeviceInfo() {
      getModality(this.deviceId).then(response => {
        this.deviceInfo = response.data;
        // 获取部门名称
        if (this.deviceInfo && this.deviceInfo.deptId) {
          listDept().then(deptResponse => {
            const depts = deptResponse.data || [];
            const dept = depts.find(d => d.deptId === this.deviceInfo.deptId);
            if (dept) {
              this.deviceInfo.deptName = dept.deptName;
            } else {
              this.deviceInfo.deptName = '未知部门';
            }
          });
        }
      }).catch(error => {
        console.error("获取设备信息失败:", error);
        this.$modal.msgError("获取设备信息失败");
      });
    },

    /** 查询设备规则列表 */
    getDeviceRules() {
      this.loading = true;
      getDeviceRules(this.deviceId).then(response => {
        this.deviceRuleList = response.data || [];
        this.total = this.deviceRuleList.length;
        this.loading = false;
      }).catch(error => {
        console.error("获取设备规则列表失败:", error);
        this.loading = false;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      // 本地过滤规则列表
      this.getDeviceRules();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    },

    /** 分配规则按钮操作 */
    handleAssignRule() {
      this.ruleDialogVisible = true;
      this.selectedRuleIds = [];
      this.getRuleList();
    },

    /** 获取所有规则列表 */
    getRuleList() {
      this.ruleLoading = true;
      listRule(this.ruleQueryParams).then(response => {
        this.allRuleList = response.rows;
        this.ruleTotal = response.total;
        this.ruleLoading = false;

        // 预选中已分配的规则
        this.$nextTick(() => {
          if (this.deviceRuleList && this.deviceRuleList.length > 0 && this.$refs.ruleTable) {
            const existingRuleIds = this.deviceRuleList.map(rule => rule.ruleId);
            this.allRuleList.forEach(row => {
              if (existingRuleIds.includes(row.ruleId)) {
                this.$refs.ruleTable.toggleRowSelection(row, true);
              }
            });
          }
        });
      });
    },

    /** 规则搜索按钮操作 */
    handleRuleQuery() {
      this.ruleQueryParams.pageNum = 1;
      this.getRuleList();
    },

    /** 重置规则搜索 */
    resetRuleQuery() {
      this.resetForm("ruleQueryForm");
      this.handleRuleQuery();
    },

    /** 规则多选框选中数据 */
    handleRuleSelectionChange(selection) {
      this.selectedRuleIds = selection.map(item => item.ruleId);
    },

    /** 分配规则 */
    handleAssignRules() {
      if (this.selectedRuleIds.length === 0) {
        this.$modal.msgError("请选择要分配的规则");
        return;
      }

      assignRules(this.deviceId, this.selectedRuleIds).then(() => {
        this.$modal.msgSuccess("规则分配成功");
        this.ruleDialogVisible = false;
        this.getDeviceRules();
      });
    },

    /** 查看规则详情 */
    handleViewRule(row) {
      getRule(row.ruleId).then(response => {
        this.currentRule = response.data;
        this.ruleDetailVisible = true;
      });
    },

    /** 移除规则 */
    handleRemoveRule(row) {
      this.$modal.confirm('确认要移除"' + row.ruleName + '"规则吗？').then(() => {
        return removeDeviceRule(this.deviceId, row.ruleId);
      }).then(() => {
        this.getDeviceRules();
        this.$modal.msgSuccess("移除成功");
      }).catch(() => {});
    },

    /** 获取部门树 */
    getDeptTree() {
      listDept().then(response => {
        // 构建部门ID和名称的映射
        const depts = response.data || [];
        depts.forEach(dept => {
          this.deptLabelMap[dept.deptId] = dept.deptName;
        });
      });
    },

    /** 初始化检查部位选项 */
    initBodyPartOptions() {

      this.bodyPartOptions = [];

      // 尝试从字典中获取数据
      if (this.dict.type.study_body_part_code && this.dict.type.study_body_part_code.length > 0) {
        this.bodyPartOptions = this.dict.type.study_body_part_code.map(item => {
          return { value: item.dictValue, label: item.dictLabel };
        });
      } else if (this.dict.type.study_body_part && this.dict.type.study_body_part.length > 0) {
        this.bodyPartOptions = this.dict.type.study_body_part.map(item => {
          return { value: item.dictValue, label: item.dictLabel };
        });
      }

      // 如果字典为空，尝试从后端获取数据
      this.getDictDataByType('study_body_part_code').then(response => {
        if (response && response.rows && response.rows.length > 0) {
          this.bodyPartOptions = response.rows.map(item => {
            return { value: item.dictValue, label: item.dictLabel };
          });
        } else if (response && response.data && response.data.length > 0) {
          this.bodyPartOptions = response.data.map(item => {
            return { value: item.dictValue, label: item.dictLabel };
          });
        }
      }).catch(error => {
        console.error("获取字典数据失败:", error);
      });
    },

    /** 从后端获取字典数据 */
    getDictDataByType(dictType) {
      return listData({
        dictType: dictType,
        pageSize: 100  // 确保获取足够的数据
      });
    },

    /** 初始化患者来源选项 */
    initPatientSourceOptions() {

      this.patientSourceOptions = [];

      // 尝试从字典中获取数据
      if (this.dict.type.study_patient_source && this.dict.type.study_patient_source.length > 0) {
        this.patientSourceOptions = this.dict.type.study_patient_source.map(item => {
          return { value: item.dictValue, label: item.dictLabel };
        });
      }

      // 如果字典为空，尝试从后端获取数据
      this.getDictDataByType('study_patient_source').then(response => {
        if (response && response.rows && response.rows.length > 0) {
          this.patientSourceOptions = response.rows.map(item => {
            return { value: item.dictValue, label: item.dictLabel };
          });
        } else if (response && response.data && response.data.length > 0) {
          this.patientSourceOptions = response.data.map(item => {
            return { value: item.dictValue, label: item.dictLabel };
          });
        }
      }).catch(error => {
        console.error("获取字典数据失败:", error);
      });
    },

    /** 获取条件类型标签 */
    getConditionTypeLabel(type) {

      if (!type) return '';

      const conditionTypes = this.dict.type.rule_condition_type || [];
      const found = conditionTypes.find(item => item.value === type);
      return found ? found.label : type;
    },

    /** 获取操作符标签 */
    getOperatorLabel(operator) {
      if (!operator) return '';

      const operatorMap = {
        '=': '等于',
        '!=': '不等于',
        'in': '包含于',
        'not_in': '不包含于',
        'between': '区间'
      };

      return operatorMap[operator] || operator;
    },

    /** 格式化时间范围 */
    formatTimeRange(value) {
      if (!value) return '';

      const times = value.split(',');
      if (times.length !== 2) return value;

      return `${times[0] || ''} 至 ${times[1] || ''}`;
    },

    /** 获取条件标签 */
    getConditionTags(conditionValue, conditionType) {
      if (!conditionValue) {
        return [];
      }

      // 如果条件值为空或只有逗号，返回空数组
      if (conditionValue === ',' || conditionValue === '') {
        return [];
      }

      const values = conditionValue.split(',').filter(v => v !== '');
      if (values.length === 0) {
        return [];
      }

      const tags = [];

      // 根据条件类型获取标签
      switch (conditionType) {
        case 'dept':
          values.forEach(value => {
            tags.push({
              value: value,
              label: this.deptLabelMap[value] || value
            });
          });
          break;
        case 'body_part':
          values.forEach(value => {
            const option = this.bodyPartOptions.find(o => o.value === value);
            tags.push({
              value: value,
              label: option ? option.label : value
            });
          });
          break;
        case 'patient_source':
          values.forEach(value => {
            const option = this.patientSourceOptions.find(o => o.value === value);
            tags.push({
              value: value,
              label: option ? option.label : value
            });
          });
          break;
        default:
          values.forEach(value => {
            tags.push({
              value: value,
              label: value
            });
          });
      }

      return tags;
    }
  }
};
</script>

<style scoped>
.condition-item {
  margin-bottom: 20px;
  padding: 10px;
  border-radius: 4px;
  background-color: #f9f9f9;
}

.mb8 {
  margin-bottom: 8px;
}

/* 固定大小的项目 */
.fixed-size-item {
  height: auto;
}

/* 固定内容区域 - 用于条件类型和操作符 */
.fixed-content {
  min-height: 40px;
  height: 40px;
  overflow-y: auto;
  padding: 8px;
  word-break: break-all;
}

/* 条件值内容区域 - 动态高度 */
.condition-value-content {
  min-height: 40px;
  height: auto;
  max-height: none;
  overflow: visible;
}

/* 标签容器 */
.tag-container {
  display: flex;
  flex-wrap: wrap;
  padding: 5px 0;
}

/* 自定义 el-descriptions-item 样式 */
::v-deep .el-descriptions-item__label {
  width: 120px;
  background-color: #f5f7fa;
}

::v-deep .el-descriptions-item__content {
  min-width: 200px;
}
</style>
