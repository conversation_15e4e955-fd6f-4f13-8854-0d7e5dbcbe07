import request from '@/utils/request'

// 查询设备排班列表
export function listDevicePlan(query) {
  return request({
    url: '/device/plan/list',
    method: 'get',
    params: query
  })
}

// 查询设备排班详细
export function getDevicePlan(planId) {
  return request({
    url: '/device/plan/' + planId,
    method: 'get'
  })
}

// 新增设备排班
export function addDevicePlan(data) {
  return request({
    url: '/device/plan',
    method: 'post',
    data: data
  })
}

// 修改设备排班
export function updateDevicePlan(data) {
  return request({
    url: '/device/plan',
    method: 'put',
    data: data
  })
}

// 删除设备排班
export function delDevicePlan(planId) {
  return request({
    url: '/device/plan/' + planId,
    method: 'delete'
  })
}

// 查询设备列表
export function listDevice(query) {
  return request({
    url: '/device/list',
    method: 'get',
    params: query
  })
}

// 查询模板列表
export function listTemplate(query) {
  return request({
    url: '/device/template/list',
    method: 'get',
    params: query
  })
}
