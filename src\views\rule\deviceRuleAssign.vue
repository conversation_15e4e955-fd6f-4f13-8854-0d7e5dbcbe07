<template>
  <div class="app-container">
    <!-- 搜索区域 -->
    <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" label-width="68px">
      <el-form-item label="设备名称" prop="deviceName">
        <el-input
          v-model="queryParams.deviceName"
          placeholder="请输入设备名称"
          clearable
          @keyup.enter.native="handleQuery"
        />
      </el-form-item>
      <el-form-item label="所属部门" prop="deptId">
        <el-select v-model="queryParams.deptId" placeholder="请选择部门" clearable @change="handleQuery">
          <el-option
            v-for="dept in deptOptions"
            :key="dept.deptId"
            :label="dept.deptName"
            :value="dept.deptId"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
        <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
      </el-form-item>
    </el-form>

    <!-- 设备列表 -->
    <el-table v-loading="loading" :data="deviceList" highlight-current-row>
      <el-table-column label="设备ID" align="center" prop="deviceId" width="80" />
      <el-table-column label="设备名称" align="center" prop="deviceName" />
      <el-table-column label="设备类型" align="center" prop="deviceType" />
      <el-table-column label="设备位置" align="center" prop="deviceLocation" />
      <el-table-column label="所属部门" align="center" prop="deptName" />
      <el-table-column label="操作" align="center" class-name="small-padding fixed-width">
        <template slot-scope="scope">
          <router-link :to="'/device/rule-manage/index/' + scope.row.deviceId" class="link-type">
            <el-button
              size="mini"
              type="text"
              icon="el-icon-setting"
              v-hasPermi="['device:rule:assign']"
            >管理规则</el-button>
          </router-link>
        </template>
      </el-table-column>
    </el-table>

    <pagination
      v-show="total>0"
      :total="total"
      :page.sync="queryParams.pageNum"
      :limit.sync="queryParams.pageSize"
      @pagination="getDeviceList"
    />




  </div>
</template>

<script>
import { listDept } from "@/api/system/dept";
import { listDevice } from "@/api/device/plan";

export default {
  name: "DeviceRuleAssign",
  data() {
    return {
      // 遮罩层
      loading: true,
      // 部门选项
      deptOptions: [],
      // 设备列表
      deviceList: [],
      // 总条数
      total: 0,
      // 设备查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceName: null,
        deptId: null
      }
    };
  },
  created() {
    this.getDeptOptions();
    this.getDeviceList();
  },
  methods: {
    /** 获取部门选项 */
    getDeptOptions() {
      listDept().then(response => {
        this.deptOptions = response.data || [];
      });
    },

    /** 获取设备列表 */
    getDeviceList() {
      this.loading = true;
      listDevice(this.queryParams).then(response => {
        this.deviceList = response.rows || [];
        this.total = response.total || 0;

        // 处理部门名称
        if (this.deviceList.length > 0) {
          this.deviceList.forEach(device => {
            // 根据部门ID查找部门名称
            const dept = this.deptOptions.find(d => d.deptId === device.deptId);
            device.deptName = dept ? dept.deptName : '未知部门';
          });
        }

        this.loading = false;
      }).catch(error => {
        console.error("获取设备列表失败:", error);
        this.loading = false;
      });
    },

    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getDeviceList();
    },

    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      this.handleQuery();
    }
  }
};
</script>

<style scoped>
.link-type {
  color: #409EFF;
}
</style>
