<template>
  <div :class="className" :style="{height:height,width:width}" />
</template>

<script>
import * as echarts from 'echarts'
require('echarts/theme/macarons') // echarts theme
import resize from './mixins/resize'

export default {
  mixins: [resize],
  props: {
    className: {
      type: String,
      default: 'chart'
    },
    width: {
      type: String,
      default: '100%'
    },
    height: {
      type: String,
      default: '300px'
    },
    chartData: {
      type: Array,
      required: true
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    chartData: {
      deep: true,
      handler(val) {
        this.setOptions(val)
      }
    }
  },
  mounted() {
    this.$nextTick(() => {
      this.initChart()
    })
  },
  beforeDestroy() {
    if (!this.chart) {
      return
    }
    this.chart.dispose()
    this.chart = null
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$el, 'macarons')
      this.setOptions(this.chartData)
    },
    setOptions(chartData) {
      const deviceNames = chartData.map(item => item.deviceName || item.device)
      const completedData = chartData.map(item => item.completedCount || item.completed || 0)
      const scheduledData = chartData.map(item => item.scheduledCount || item.scheduled || 0)

      this.chart.setOption({
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {
          data: ['已完成检查', '计划检查'],
          top: 0
        },
        grid: {
          top: 30,
          left: '2%',
          right: '2%',
          bottom: '3%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          data: deviceNames,
          axisTick: {
            alignWithLabel: true
          },
          axisLabel: {
            interval: 0,
            rotate: 30
          }
        },
        yAxis: {
          type: 'value',
          name: '检查数量',
          nameTextStyle: {
            padding: [0, 0, 0, 40]
          },
          minInterval: 1
        },
        series: [
          {
            name: '已完成检查',
            type: 'bar',
            stack: '总量',
            barWidth: '40%',
            data: completedData,
            itemStyle: {
              color: '#67C23A'
            },
            label: {
              show: true,
              position: 'inside'
            }
          },
          {
            name: '计划检查',
            type: 'bar',
            stack: '总量',
            barWidth: '40%',
            data: scheduledData,
            itemStyle: {
              color: '#409EFF'
            },
            label: {
              show: true,
              position: 'inside'
            }
          }
        ]
      })
    }
  }
}
</script>
