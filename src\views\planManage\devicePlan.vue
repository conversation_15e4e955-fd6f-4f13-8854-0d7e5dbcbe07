<template>
  <div class="app-container">
    <el-row :gutter="20">
      <!--部门数据-->
      <el-col :span="4" :xs="24">
        <div class="head-container">
          <el-input
            v-model="deptName"
            placeholder="请输入部门名称"
            clearable
            size="small"
            prefix-icon="el-icon-search"
            style="margin-bottom: 20px"
          />
        </div>
        <div class="head-container">
          <el-tree
            :data="treeData"
            :props="defaultProps"
            :expand-on-click-node="false"
            :filter-node-method="filterNode"
            ref="tree"
            node-key="id"
            default-expand-all
            highlight-current
            @node-click="handleNodeClick"
          >
            <span class="custom-tree-node" slot-scope="{ node, data }">
              <span>
                <i :class="data.type === 'dept' ? 'el-icon-office-building' : 'el-icon-monitor'"></i>
                {{ node.label }}
              </span>
            </span>
          </el-tree>
        </div>
      </el-col>

      <!--设备排班数据-->
      <el-col :span="20" :xs="24">
        <el-form :model="queryParams" ref="queryForm" size="small" :inline="true" v-show="showSearch" label-width="68px">
          <el-form-item label="设备名称" prop="deviceName">
            <el-input
              v-model="queryParams.deviceName"
              placeholder="请输入设备名称"
              clearable
              @keyup.enter.native="handleQuery"
              style="width: 240px"
            />
          </el-form-item>
          <!-- <el-form-item label="选择设备" prop="deviceId" v-if="!currentDeviceId && queryParams.deptId">
            <el-select
              v-model="queryParams.deviceId"
              placeholder="请选择设备"
              clearable
              style="width: 240px"
              @change="handleDeviceSelectChange"
            >
              <el-option
                v-for="item in deptDeviceOptions"
                :key="item.deviceId"
                :label="item.deviceName"
                :value="item.deviceId"
              />
            </el-select>
          </el-form-item> -->
          <el-form-item label="计划日期" prop="planDate">
            <el-date-picker
              v-model="queryParams.planDate"
              type="date"
              placeholder="选择日期"
              value-format="yyyy-MM-dd"
              style="width: 240px"
            />
          </el-form-item>
          <el-form-item>
            <el-button type="primary" icon="el-icon-search" size="mini" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>

        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              type="primary"
              plain
              icon="el-icon-plus"
              size="mini"
              @click="handleAdd"
              v-hasPermi="['device:plan:add']"
              :disabled="!currentDeviceId && !queryParams.deptId"
            >新增</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="success"
              plain
              icon="el-icon-edit"
              size="mini"
              :disabled="single"
              @click="handleUpdate"
              v-hasPermi="['device:plan:edit']"
            >修改</el-button>
          </el-col>
          <el-col :span="1.5">
            <el-button
              type="danger"
              plain
              icon="el-icon-delete"
              size="mini"
              :disabled="multiple"
              @click="handleDelete"
              v-hasPermi="['device:plan:remove']"
            >删除</el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <div class="table-container" style="overflow-x: auto;">
          <el-table
            v-loading="loading"
            :data="planList"
            @selection-change="handleSelectionChange"
            style="width: 100%; min-width: 1000px;"
            :header-cell-style="{background:'#eef1f6',color:'#606266'}"
            border
          >
            <el-table-column type="selection" width="55" align="center" />
            <el-table-column label="计划ID" align="center" prop="planId" width="80" />
            <el-table-column label="设备名称" align="center" min-width="120" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <span>{{ scope.row.device.deviceName || '未知设备' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="星期" align="center" prop="week" width="80">
              <template slot-scope="scope">
                <span>{{ formatWeek(scope.row.week) }}</span>
              </template>
            </el-table-column>

            <el-table-column label="计划日期" align="center" prop="planDate" width="100">
              <template slot-scope="scope">
                <span>{{ parseTime(scope.row.planDate, '{y}-{m}-{d}') }}</span>
              </template>
            </el-table-column>
            <el-table-column label="班次" align="center" prop="planCount" width="80">
              <template slot-scope="scope">
                <dict-tag :options="dict.type.plan_count" :value="scope.row.planCount"/>
              </template>
            </el-table-column>
            <el-table-column label="开始时间" align="center" prop="stime" width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.stime || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="结束时间" align="center" prop="etime" width="100">
              <template slot-scope="scope">
                <span>{{ scope.row.etime || '-' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="模板名称" align="center" min-width="120" :show-overflow-tooltip="true">
              <template slot-scope="scope">
                <span>{{ scope.row.template && scope.row.template.templateName ? scope.row.template.templateName : '无模板' }}</span>
              </template>
            </el-table-column>
            <el-table-column label="普通号" align="center" prop="ordinaryPlan" width="80">
              <template slot-scope="scope">
                <span style="color: #67C23A; font-weight: bold;">{{ scope.row.ordinaryPlan }}</span>
              </template>
            </el-table-column>
            <el-table-column label="急诊号" align="center" prop="emergencyPlan" width="80">
              <template slot-scope="scope">
                <span style="color: #F56C6C; font-weight: bold;">{{ scope.row.emergencyPlan }}</span>
              </template>
            </el-table-column>
            <el-table-column label="预留号" align="center" prop="reservePlan" width="80">
              <template slot-scope="scope">
                <span style="color: #409EFF; font-weight: bold;">{{ scope.row.reservePlan }}</span>
              </template>
            </el-table-column>
            <el-table-column label="总号数" align="center" prop="totalPlan" width="80">
              <template slot-scope="scope">
                <span style="color: #E6A23C; font-weight: bold; font-size: 14px;">{{ scope.row.totalPlan }}</span>
              </template>
            </el-table-column>
            <el-table-column label="操作" align="center" class-name="small-padding fixed-width" width="150" fixed="right">
              <template slot-scope="scope">
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-edit"
                  @click="handleUpdate(scope.row)"
                  v-hasPermi="['device:plan:edit']"
                >修改</el-button>
                <el-button
                  size="mini"
                  type="text"
                  icon="el-icon-delete"
                  @click="handleDelete(scope.row)"
                  v-hasPermi="['device:plan:remove']"
                >删除</el-button>
              </template>
            </el-table-column>
          </el-table>
        </div>

        <pagination
          v-show="total>0"
          :total="total"
          :page.sync="queryParams.pageNum"
          :limit.sync="queryParams.pageSize"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 添加设备排班对话框 -->
    <el-dialog title="添加设备排班" :visible.sync="openAdd" width="800px" append-to-body :close-on-click-modal="false">
      <el-form ref="addForm" :model="form" :rules="rules" label-width="100px">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="设备" prop="deviceId">
              <el-select v-model="form.deviceId" placeholder="请选择设备" style="width: 100%" v-if="!currentDeviceId && deptDeviceOptions.length > 0">
                <el-option
                  v-for="item in deptDeviceOptions"
                  :key="item.deviceId"
                  :label="item.deviceName"
                  :value="item.deviceId"
                />
              </el-select>
              <el-input :value="getDeviceNameForDisplay()" disabled v-else />
              <input type="hidden" v-model="form.deviceId" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="模板" prop="templateId">
              <el-select v-model="form.templateId" placeholder="请选择模板" style="width: 100%" @change="handleTemplateChange" clearable>
                <el-option
                  v-for="item in templateOptions"
                  :key="item.templateId"
                  :label="item.templateName"
                  :value="item.templateId"
                />
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>

        <el-row :gutter="20">
          <el-col :span="12">
            <el-form-item label="星期" prop="week">
              <el-select v-model="form.week" placeholder="请选择星期" style="width: 100%" @change="handleWeekChange" :disabled="!!form.templateId">
                <el-option label="星期一" value="星期一" />
                <el-option label="星期二" value="星期二" />
                <el-option label="星期三" value="星期三" />
                <el-option label="星期四" value="星期四" />
                <el-option label="星期五" value="星期五" />
                <el-option label="星期六" value="星期六" />
                <el-option label="星期日" value="星期日" />
              </el-select>
              <div class="el-form-item__tip" v-if="form.templateId">
                <small style="color: #909399;">模板已选择星期</small>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="计划日期" prop="planDate">
              <el-input v-model="form.planDate" disabled>
                <template slot="append">
                  <el-tooltip content="根据星期自动计算的日期" placement="top">
                    <i class="el-icon-date"></i>
                  </el-tooltip>
                </template>
              </el-input>
            </el-form-item>
          </el-col>
        </el-row>

        <el-form-item label="描述" prop="planDescribe">
          <el-input
            v-model="form.planDescribe"
            type="textarea"
            :rows="2"
            placeholder="排班描述"
            maxlength="300"
            show-word-limit
            :disabled="true"
          />
        </el-form-item>

        <!-- 时间段列表 -->
        <el-divider content-position="center">时间段设置</el-divider>
        <div v-for="(timeSlot, index) in form.planDetailList" :key="index" class="time-slot-container">
          <el-card class="time-slot-card">
            <div slot="header" class="clearfix">
              <span>时间段 {{ index + 1 }}</span>
              <span style="margin-left: 15px;">
                班次:
                <el-select
                  v-model="timeSlot.planCount"
                  placeholder="请选择班次"
                  size="mini"
                  style="width: 100px;"
                  @change="() => handlePlanCountChange(index)"
                >
                  <el-option
                    v-for="dict in dict.type.plan_count"
                    :key="dict.value"
                    :label="dict.label"
                    :value="parseInt(dict.value)"
                  />
                </el-select>
              </span>
              <el-button
                style="float: right; padding: 3px 0"
                type="text"
                icon="el-icon-delete"
                @click="removeTimeSlot(index)"
              >删除</el-button>
            </div>

            <el-row :gutter="20">
              <el-col :span="12">
                <el-form-item :label="'开始时间'" :prop="'planDetailList.' + index + '.stime'" :rules="[
                  { required: true, message: '开始时间不能为空', trigger: 'change' }
                ]">
                  <el-time-select
                    v-model="timeSlot.stime"
                    placeholder="选择开始时间"
                    :picker-options="{
                      start: '00:00',
                      end: '23:45',
                      step: '00:15'
                    }"
                    style="width: 100%"
                  >
                  </el-time-select>
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item :label="'结束时间'" :prop="'planDetailList.' + index + '.etime'" :rules="[
                  { required: true, message: '结束时间不能为空', trigger: 'change' },
                  { validator: validateEndTime, trigger: 'change' }
                ]">
                  <el-time-select
                    v-model="timeSlot.etime"
                    placeholder="选择结束时间"
                    :picker-options="{
                      start: '00:00',
                      end: '23:45',
                      step: '00:15',
                      minTime: timeSlot.stime
                    }"
                    style="width: 100%"
                  >
                  </el-time-select>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row :gutter="20">
              <el-col :span="8">
                <el-form-item :label="'普通号'" :prop="'planDetailList.' + index + '.ordinaryPlan'" :rules="[
                  { required: true, message: '普通号不能为空', trigger: 'blur' }
                ]">
                  <div style="display: flex; align-items: center;">
                    <el-input-number v-model="timeSlot.ordinaryPlan" :min="0" :max="100" @change="calculateTimeSlotTotal(index)" />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="'急诊号'" :prop="'planDetailList.' + index + '.emergencyPlan'" :rules="[
                  { required: true, message: '急诊号不能为空', trigger: 'blur' }
                ]">
                  <div style="display: flex; align-items: center;">
                    <el-input-number v-model="timeSlot.emergencyPlan" :min="0" :max="100" @change="calculateTimeSlotTotal(index)" />
                  </div>
                </el-form-item>
              </el-col>
              <el-col :span="8">
                <el-form-item :label="'预留号'" :prop="'planDetailList.' + index + '.reservePlan'" :rules="[
                  { required: true, message: '预留号不能为空', trigger: 'blur' }
                ]">
                  <div style="display: flex; align-items: center;">
                    <el-input-number v-model="timeSlot.reservePlan" :min="0" :max="100" @change="calculateTimeSlotTotal(index)" />
                  </div>
                </el-form-item>
              </el-col>
            </el-row>

            <el-row>
              <el-col :span="24">
                <div class="time-slot-total">
                  <span>时间段总号数：</span>
                  <span style="color: #E6A23C; font-weight: bold;">{{ timeSlot.ordinaryPlan + timeSlot.emergencyPlan + timeSlot.reservePlan }}</span>
                </div>
              </el-col>
            </el-row>
          </el-card>
        </div>

        <el-divider content-position="center">总计</el-divider>
        <el-form-item label="总号数1">
          <div style="display: flex; align-items: center;">
            <el-input-number v-model="totalTimeSlotPlan"  disabled />
           
          </div>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="addTimeSlot">添加</el-button>
        <el-button type="primary" @click="submitAddForm">确 定</el-button>
        <el-button @click="cancelAdd">取 消</el-button>
      </div>
    </el-dialog>

    <!-- 修改设备排班对话框 -->
    <el-dialog title="修改设备排班" :visible.sync="openEdit" width="650px" append-to-body :close-on-click-modal="false">
      <el-form ref="editForm" :model="editForm" :rules="rules" label-width="80px">
        <div class="edit-info-box">
          <div class="edit-info-item">
            <span class="edit-info-label">设备：</span>
            <span class="edit-info-value">{{ editForm.device ? editForm.device.deviceName : '' }}</span>
          </div>
          <div class="edit-info-item">
            <span class="edit-info-label">日期：</span>
            <span class="edit-info-value">{{ editForm.planDate }}</span>
          </div>
          <div class="edit-info-item">
            <span class="edit-info-label">星期：</span>
            <span class="edit-info-value">{{ editForm.week }}</span>
          </div>
          <div class="edit-info-item">
            <span class="edit-info-label">模板：</span>
            <span class="edit-info-value">{{ editForm.template && editForm.template.templateName ? editForm.template.templateName : '无模板' }}</span>
          </div>
        </div>

        <!-- 时间段列表 -->
        <div class="time-slot-list">
          <div v-for="(timeSlot, index) in editForm.planDetailList" :key="index" class="time-slot-item">
            <div class="time-slot-header">
              <span class="time-slot-index">
                班次:
                <el-select
                  v-model="timeSlot.planCount"
                  placeholder="请选择班次"
                  size="mini"
                  style="width: 100px;"
                  @change="() => handleEditPlanCountChange(index)"
                >
                  <el-option
                    v-for="dict in dict.type.plan_count"
                    :key="dict.value"
                    :label="dict.label"
                    :value="parseInt(dict.value)"
                  />
                </el-select>
              </span>
              <el-button
                size="mini"
                type="text"
                icon="el-icon-delete"
                @click="removeEditTimeSlot(index)"
                class="delete-btn"
              >删除</el-button>
            </div>
            <div class="time-row">
              <el-time-select
                v-model="editForm.planDetailList[index].stime"
                placeholder="开始时间"
                :picker-options="{
                  start: '00:00',
                  end: '23:45',
                  step: '00:15'
                }"
                size="mini"
                style="width: 110px; margin-right: 5px;"
                @input="(val) => handleTimeInput(index, 'stime', val)"
              >
              </el-time-select>
              <span style="margin: 0 5px;">至</span>
              <el-time-select
                v-model="editForm.planDetailList[index].etime"
                placeholder="结束时间"
                :picker-options="{
                  start: '00:00',
                  end: '23:45',
                  step: '00:15',
                  minTime: editForm.planDetailList[index].stime
                }"
                size="mini"
                style="width: 110px;"
                @input="(val) => handleTimeInput(index, 'etime', val)"
              >
              </el-time-select>
            </div>
            <div class="number-row">
              <div class="number-item">
                <span class="number-label">普通号:</span>
                <el-input-number
                  v-model="timeSlot.ordinaryPlan"
                  :min="0"
                  :max="100"
                  size="mini"
                  controls-position="right"
                  @change="() => calculateEditTimeSlotTotal(index)"
                />
              </div>
              <div class="number-item">
                <span class="number-label">急诊号:</span>
                <el-input-number
                  v-model="timeSlot.emergencyPlan"
                  :min="0"
                  :max="100"
                  size="mini"
                  controls-position="right"
                  @change="() => calculateEditTimeSlotTotal(index)"
                />
              </div>
              <div class="number-item">
                <span class="number-label">预留号:</span>
                <el-input-number
                  v-model="timeSlot.reservePlan"
                  :min="0"
                  :max="100"
                  size="mini"
                  controls-position="right"
                  @change="() => calculateEditTimeSlotTotal(index)"
                />
              </div>
            </div>
          </div>
        </div>

        <div class="edit-buttons">
          <div class="edit-total">
            <span>总号数：</span>
            <span class="edit-total-number">{{ editForm.totalPlan }}</span>
          </div>
        </div>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" @click="submitEditForm">确 定</el-button>
        <el-button @click="cancelEdit">取 消</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
// 导入设备排班相关API
import { listDevicePlan, getDevicePlan, delDevicePlan, addDevicePlan, updateDevicePlan, listDevice, listTemplate } from "@/api/device/plan";
import { deptTreeSelect } from "@/api/system/user";

export default {
  name: "DevicePlan",
  dicts: ['plan_count'],
  data() {
    // 获取今天的日期格式化为 yyyy-MM-dd
    const today = new Date();
    const year = today.getFullYear();
    const month = (today.getMonth() + 1).toString().padStart(2, '0');
    const day = today.getDate().toString().padStart(2, '0');
    const todayFormatted = `${year}-${month}-${day}`;

    return {
      // 遮罩层
      loading: true,
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: true,
      // 总条数
      total: 0,
      // 设备排班表格数据
      planList: [],
      // 设备列表
      deviceList: [],
      // 模板选项
      templateOptions: [],
      // 部门下的设备选项（用于表单选择）
      deptDeviceOptions: [],
      // 是否显示新增弹出层
      openAdd: false,
      // 是否显示修改弹出层
      openEdit: false,
      // 部门名称
      deptName: "",
      // 树形数据（包含部门和设备）
      treeData: [],
      // 当前选中的设备ID
      currentDeviceId: null,
      // 当前选中的设备名称
      deviceName: "",
      // 树形配置选项
      defaultProps: {
        children: "children",
        label: "label"
      },
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        deviceId: null,
        deviceName: null,
        planDate: todayFormatted // 默认为今天的日期
      },
      // 新增表单参数
      form: {},
      // 修改表单参数
      editForm: {},
      // 表单校验
      rules: {
        deviceId: [
          { required: true, message: "设备不能为空", trigger: "blur" }
        ],
        // 模板不是必填项
        templateId: [],
        week: [
          { required: true, message: "星期不能为空", trigger: "change" }
        ],
        planCount: [
          { required: true, message: "班次不能为空", trigger: "change" }
        ],
        stime: [
          { required: true, message: "开始时间不能为空", trigger: "change" }
        ],
        etime: [
          { required: true, message: "结束时间不能为空", trigger: "change" },
          { validator: this.validateEndTime, trigger: "change" }
        ],
        ordinaryPlan: [
          { required: true, message: "普通号不能为空", trigger: "blur" }
        ],
        emergencyPlan: [
          { required: true, message: "急诊号不能为空", trigger: "blur" }
        ],
        reservePlan: [
          { required: true, message: "预留号不能为空", trigger: "blur" }
        ],
        totalPlan: [
          { validator: this.validateTotalPlan, trigger: "blur" }
        ]
      },
      totalTimeSlotPlan: 0, // 添加时间段总号数
    };
  },
  watch: {
    // 根据名称筛选部门树
    deptName(val) {
      this.$refs.tree.filter(val);
    },
    // 监听开始时间变化，重新验证结束时间
    'form.stime': function(val) {
      // 如果开始时间和结束时间都存在，重新验证结束时间
      console.log("开始时间变化:", val);
      if (val && this.form.etime && this.$refs.form) {
        // 添加检查确保 this.$refs.form 存在
        this.$nextTick(() => {
          // 使用 nextTick 确保 DOM 已经更新
          if (this.$refs.form) {
            this.$refs.form.validateField('etime');
          }
        });
      }
    }
  },
  created() {
    // 初始化时先将loading设置为false，避免一直转圈
    this.loading = false;
    this.getDeptTree();
    this.getTemplateOptions();

    // 获取当前用户信息和部门ID
    this.getCurrentUserDept();
  },
  methods: {
    /** 验证结束时间不能小于开始时间 */
    validateEndTime(rule, value, callback) {
      // 根据当前表单类型获取正确的开始时间
      let startTime = '';
      if (this.openAdd) {
        startTime = this.form.stime;
      } else if (this.openEdit) {
        // 在修改表单中，我们需要从时间段列表中找到对应的开始时间
        // 这里假设验证是针对时间段列表中的某一项
        // 从rule.field中提取索引，例如 'planDetailList.0.etime'
        const fieldPath = rule.field || '';
        const match = fieldPath.match(/planDetailList\.(\d+)\.etime/);
        if (match && match[1]) {
          const index = parseInt(match[1]);
          if (this.editForm.planDetailList && this.editForm.planDetailList[index]) {
            startTime = this.editForm.planDetailList[index].stime;
          }
        }
      }

      if (startTime && value) {
        // 将时间字符串转换为分钟数进行比较
        const startMinutes = this.timeToMinutes(startTime);
        const endMinutes = this.timeToMinutes(value);

        if (endMinutes <= startMinutes) {
          callback(new Error('结束时间必须大于开始时间'));
        } else {
          callback();
        }
      } else {
        callback();
      }
    },

    /** 验证总号数必须大于0 */
    validateTotalPlan(rule, value, callback) {
      const totalPlan = parseInt(value, 10);
      if (isNaN(totalPlan) || totalPlan <= 0) {
        callback(new Error('总号数必须大于0'));
      } else {
        callback();
      }
    },

    /** 将时间字符串转换为分钟数 */
    timeToMinutes(timeStr) {
      if (!timeStr) return 0;
      const parts = timeStr.split(':');
      return parseInt(parts[0]) * 60 + parseInt(parts[1]);
    },

    /** 获取要显示的设备名称 */
    getDeviceNameForDisplay() {
      // 直接返回设备名称，因为响应数据中始终会包含 device.deviceName
      return this.form.device ? this.form.device.deviceName : '';
    },

    /** 查询部门下拉树结构 */
    getDeptTree() {
      deptTreeSelect().then(response => {
        // 先保存原始部门数据，并添加类型标记
        const deptData = this.addTypeFlag(response.data, 'dept');
        // 将部门数据设置到树形数据中
        this.treeData = deptData;
        // 同步加载设备数据并合并到部门树中
        this.loadDevicesForTree();
      });
    },
    /** 加载部门下的设备列表，用于表单选择 */
    loadDeptDevicesForSelect(deptId) {
      if (!deptId) return;
      console.log("加载部门下的设备列表:", deptId);
      this.deptDeviceOptions = [];
      listDevice({ deptId: deptId }).then(response => {
        this.deptDeviceOptions = response.rows || [];

      }).catch(error => {
        console.error("获取部门设备列表失败:", error);
        this.deptDeviceOptions = [];
      });
    },

    /** 加载设备数据并合并到部门树中 */
    async loadDevicesForTree() {
      try {
        this.loading = true;
        // 获取所有设备
        const response = await listDevice();
        const devices = response.rows || [];

        if (devices.length === 0) {
          console.log("没有设备数据");
          this.loading = false;
          return;
        }

        // 将设备按照deptId分组
        const devicesByDept = {};
        devices.forEach(device => {
          // 确保设备有部门ID并且是字符串类型
          let deptId = device.deptId;
          if (deptId) {
            // 将deptId转换为字符串，确保类型一致
            deptId = deptId.toString();
            if (!devicesByDept[deptId]) {
              devicesByDept[deptId] = [];
            }
            devicesByDept[deptId].push(device);
          }
        });

        // 递归将设备添加到对应部门下
        this.addDevicesToDepts(this.treeData, devicesByDept);

        // 强制更新树视图
        this.$nextTick(() => {
          // 使用深拷贝创建新的树数据，触发Vue的响应式更新
          const newTreeData = JSON.parse(JSON.stringify(this.treeData));
          this.treeData = newTreeData;
        });

        this.loading = false;
      } catch (error) {
        console.error("加载设备数据失败:", error);
        this.loading = false;
      }
    },

    /** 递归将设备添加到对应部门下 */
    addDevicesToDepts(depts, devicesByDept) {
      if (!depts || depts.length === 0) return;

      depts.forEach(dept => {
        // 将部门ID转换为字符串，确保类型一致
        const deptId = dept.id.toString();

        // 获取当前部门的设备
        const deptDevices = devicesByDept[deptId] || [];

        // 将设备转换为树节点格式
        const deviceNodes = deptDevices.map(device => ({
          id: `device_${device.deviceId}`, // 添加前缀避免与部门ID冲突
          label: device.deviceName,
          type: 'device',
          deviceId: device.deviceId,
          deviceName: device.deviceName,
          deptId: device.deptId
        }));

        // 如果部门没有children属性，创建一个空数组
        if (!dept.children) {
          dept.children = [];
        }

        // 先过滤掉原有的设备节点，保留部门节点
        const deptChildren = dept.children.filter(child => child.type === 'dept');

        // 将设备节点添加到部门的children中
        dept.children = [...deptChildren, ...deviceNodes];

        // 递归处理子部门
        if (deptChildren.length > 0) {
          this.addDevicesToDepts(deptChildren, devicesByDept);
        }
      });
    },
    /** 查询模板选项 */
    getTemplateOptions() {
      listTemplate().then(response => {
        this.templateOptions = response.rows || [];
      }).catch(error => {
        console.error("获取模板列表失败:", error);
        this.templateOptions = [];
      });
    },
    /** 筛选节点 */
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    /** 为数据添加类型标记 */
    addTypeFlag(data, type) {
      if (!data) return [];
      const result = [];
      for (const item of data) {
        // 深拷贝对象，避免修改原始数据
        const newItem = JSON.parse(JSON.stringify(item));
        newItem.type = type;
        if (newItem.children && newItem.children.length > 0) {
          newItem.children = this.addTypeFlag(newItem.children, type);
        }
        result.push(newItem);
      }
      return result;
    },


    /** 节点单击事件 */
    handleNodeClick(data) {
      if (data.type === 'device') {
        // 点击设备节点
        this.currentDeviceId = data.deviceId;
        this.queryParams.deviceId = data.deviceId;
        this.queryParams.deviceName = data.deviceName; // 设置设备名称
        this.queryParams.deptId = null; // 清空部门ID，只按设备ID查询
        this.getList();
      } else {
        // 点击部门节点
        this.queryParams.deptId = data.id;
        this.currentDeviceId = null;
        this.queryParams.deviceName = null; // 清空设备名称
        this.queryParams.deviceId = null; // 清空设备ID，按部门ID查询
        this.getList(); // 查询该部门下所有设备的排班计划

        // 加载部门下的设备列表，用于设备选择
        this.loadDeptDevicesForSelect(data.id);
      }
    },

    /** 设备选择变化事件 */
    handleDeviceSelectChange(deviceId) {
      if (deviceId) {
        // 如果选择了设备，查询该设备的排班计划
        const selectedDevice = this.deptDeviceOptions.find(item => item.deviceId === deviceId);
        if (selectedDevice) {
          this.queryParams.deviceName = selectedDevice.deviceName; // 更新设备名称输入框
        }
      } else {
        // 如果清空了设备选择，也清空设备名称
        this.queryParams.deviceName = null;
      }
      this.getList();
    },


    /** 查询设备排班列表 */
    getList() {
      // 如果既没有设备ID也没有部门ID也没有设备名称，则不查询
      if (!this.currentDeviceId && !this.queryParams.deptId && !this.queryParams.deviceName) {
        this.loading = false;
        this.planList = [];
        this.total = 0;
        return;
      }

      this.loading = true;

      listDevicePlan(this.queryParams).then(response => {
        this.planList = response.rows || [];

        this.total = response.total || 0;
        console.log("获取到的排班计划:", this.planList);
        this.loading = false;
      }).catch(error => {
        console.error("获取排班列表失败:", error);
        this.planList = [];
        this.total = 0;
        this.loading = false;
      });
    },
    // 取消按钮
    cancel() {
      this.open = false;
      this.reset();
    },
    // 表单重置
    reset() {
      this.form = {
        planId: undefined,
        deviceId: this.currentDeviceId, // 如果选中了设备，使用设备ID
        deptId: this.queryParams.deptId, // 如果选中了部门，保存部门ID
        templateId: undefined,
        planDate: undefined,
        week: undefined,
        planDescribe: undefined,
        totalPlan: 0,
        // 初始化空的device对象，避免访问 form.device.deviceName 时出错
        device: {
          deviceName: ''
        },
        // 初始化时间段列表
        planDetailList: [this.createEmptyTimeSlot()]
      };
      this.resetForm("form");
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1;
      this.getList();
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm");
      // 重置为今天的日期
      const today = new Date();
      const year = today.getFullYear();
      const month = (today.getMonth() + 1).toString().padStart(2, '0');
      const day = today.getDate().toString().padStart(2, '0');
      this.queryParams.planDate = `${year}-${month}-${day}`;
      // 如果当前选中了设备，重置为该设备
      if (this.currentDeviceId) {
        this.queryParams.deviceId = this.currentDeviceId;
        // 找到当前设备的名称
        const device = this.deviceList.find(item => item.deviceId === this.currentDeviceId);
        if (device) {
          this.queryParams.deviceName = device.deviceName;
        }
      } else {
        this.queryParams.deviceId = null;
        this.queryParams.deviceName = null;
      }
      this.handleQuery();
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.planId);
      this.single = selection.length !== 1;
      this.multiple = !selection.length;
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.reset();
      console.log("当前设备ID:", this.currentDeviceId+"::"+this.queryParams.deptId);
      // 如果选中了部门而不是设备，需要先选择设备
      if (!this.currentDeviceId && this.queryParams.deptId) {
        // 加载部门下的设备列表，用于选择
        this.loadDeptDevicesForSelect(this.queryParams.deptId);
      }
      this.form.device.deviceName = this.queryParams.deviceName; // 设置设备名称
      this.openAdd = true;
    },

    /** 修改按钮操作 */
    handleUpdate(row) {
      // 获取排班详情
      getDevicePlan(row.planId).then(response => {
        console.log('获取排班详情成功:', response.data);

        // 设置基本信息
        this.editForm = response.data;

        // 格式化日期
        if (this.editForm.planDate) {
          this.editForm.planDate = this.parseTime(this.editForm.planDate, '{y}-{m}-{d}');
        }

        // 处理时间段列表
        if (!this.editForm.planDetailList || this.editForm.planDetailList.length === 0) {
          // 如果没有时间段列表，但有单个时间段的信息，创建一个时间段
          if (this.editForm.stime && this.editForm.etime) {
            this.editForm.planDetailList = [{
              planDetailId: undefined,
              planId: this.editForm.planId,
              stime: this.editForm.stime,
              etime: this.editForm.etime,
              ordinaryPlan: this.editForm.ordinaryPlan || 0,
              emergencyPlan: this.editForm.emergencyPlan || 0,
              reservePlan: this.editForm.reservePlan || 0,
              planCount: this.editForm.planCount || 0,
              isDel: 0
            }];
          } else {
            // 如果没有任何时间信息，添加一个空的时间段
            this.editForm.planDetailList = [this.createEmptyTimeSlot()];
          }
        }

        this.openEdit = true;
      }).catch(error => {
        console.error('获取排班详情失败:', error);
        this.$message.error('获取排班详情失败');
      });
    },

    /** 取消新增 */
    cancelAdd() {
      this.openAdd = false;
      this.reset();
    },

    /** 取消修改 */
    cancelEdit() {
      this.openEdit = false;
      this.editForm = {};
    },

    /** 提交新增表单 */
    submitAddForm() {
      this.$refs["addForm"].validate(valid => {
        if (valid) {
          // 额外检查总号数是否大于0
          if (this.form.totalPlan <= 0) {
            this.$message.error("总号数必须大于0");
            return;
          }

          addDevicePlan(this.form).then(() => {
            this.$modal.msgSuccess("新增成功");
            this.openAdd = false;
            this.getList();
          });
        }
      });
    },

    /** 提交修改表单 */
    submitEditForm() {
      this.$refs["editForm"].validate(valid => {
        if (valid) {
          // 额外检查总号数是否大于0
          if (this.editForm.totalPlan <= 0) {
            this.$message.error("总号数必须大于0");
            return;
          }

          updateDevicePlan(this.editForm).then(() => {
            this.$modal.msgSuccess("修改成功");
            this.openEdit = false;
            this.getList();
          });
        }
      });
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const planIds = row.planId || this.ids;
      this.$modal.confirm('是否确认删除设备排班编号为"' + planIds + '"的数据项？').then(function() {
        return delDevicePlan(planIds);
      }).then(() => {
        this.getList();
        this.$modal.msgSuccess("删除成功");
      }).catch(() => {});
    },
    

    /** 处理班次变更 */
    handlePlanCountChange(index) {
      const timeSlot = this.form.planDetailList[index];
      if (timeSlot) {
        
        // 更新总计划数
        this.calculateTotal();
      }
    },

   

    /** 添加时间段 */
    addTimeSlot() {
      if (!this.form.planDetailList) {
        this.form.planDetailList = [];
      }
      this.form.planDetailList.push(this.createEmptyTimeSlot());
    },

    /** 移除时间段 */
    removeTimeSlot(index) {
      this.form.planDetailList.splice(index, 1);
      if (this.form.planDetailList.length === 0) {
        this.addTimeSlot();
      }
      this.calculateTotal();
    },

    /** 添加修改表单时间段 */
    addEditTimeSlot() {
      if (!this.editForm.planDetailList) {
        this.editForm.planDetailList = [];
      }
      this.editForm.planDetailList.push(this.createEmptyTimeSlot());
    },

    /** 移除修改表单时间段 */
    removeEditTimeSlot(index) {
      this.editForm.planDetailList.splice(index, 1);
      if (this.editForm.planDetailList.length === 0) {
        this.addEditTimeSlot();
      }
      this.calculateEditTotal();
    },

    /** 处理修改表单班次变更 */
    handleEditPlanCountChange(index) {
      
      const timeSlot = this.editForm.planDetailList[index];
      if (timeSlot) {
        // 强制更新视图
        this.$forceUpdate();
        // 更新总计划数
        this.calculateEditTotal();
      }
    },

    /** 计算修改表单时间段总计划数 */
    calculateEditTimeSlotTotal(index) {
      const timeSlot = this.editForm.planDetailList[index];
      if (timeSlot) {
        // 更新总计划数
        this.calculateEditTotal();
      }
    },

    /** 计算修改表单总号数 */
    calculateEditTotal() {
      if (!this.editForm.planDetailList || this.editForm.planDetailList.length === 0) {
        this.editForm.totalPlan = 0;
        return;
      }

      let total = 0;
      this.editForm.planDetailList.forEach(timeSlot => {
        total += (timeSlot.ordinaryPlan || 0) + (timeSlot.emergencyPlan || 0) + (timeSlot.reservePlan || 0);
      });

      this.editForm.totalPlan = total;
    },

    /** 创建空的时间段对象 */
    createEmptyTimeSlot() {
      return {
        planDetailId: undefined,
        planId: undefined,
        stime: undefined,
        etime: undefined,
        ordinaryPlan: 0,
        emergencyPlan: 0,
        reservePlan: 0,
        planCount: null, // 设置为 null，以便用户可以选择班次
        isDel: 0
      };
    },

    /** 计算时间段总号数 */
    calculateTimeSlotTotal(index) {
      const timeSlot = this.form.planDetailList[index];
      if (timeSlot) {
        // 计算当前时间段的总号数
        const total = (timeSlot.ordinaryPlan || 0) + (timeSlot.emergencyPlan || 0) + (timeSlot.reservePlan || 0);
        // 更新总计划数
        this.calculateTotal();
        // 计算所有时间段的总号数
        this.calculateTotalTimeSlotPlan();
      }
    },

    /** 计算所有时间段的总号数 */
    calculateTotalTimeSlotPlan() {
      if (!this.form.planDetailList || this.form.planDetailList.length === 0) {
        this.totalTimeSlotPlan = 0;
        return;
      }

      let total = 0;
      this.form.planDetailList.forEach(timeSlot => {
        total += (timeSlot.ordinaryPlan || 0) + (timeSlot.emergencyPlan || 0) + (timeSlot.reservePlan || 0);
      });

      this.totalTimeSlotPlan = total;
    },

    /** 计算总号数 */
    calculateTotal() {
      if (!this.form.planDetailList || this.form.planDetailList.length === 0) {
        this.form.totalPlan = 0;
        return;
      }

      let total = 0;
      this.form.planDetailList.forEach(timeSlot => {
        total += (timeSlot.ordinaryPlan || 0) + (timeSlot.emergencyPlan || 0) + (timeSlot.reservePlan || 0);
      });

      this.form.totalPlan = total;

      // 触发总号数验证
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.validateField('totalPlan');
        }
      });
    },
    /** 模板变更事件 */
    handleTemplateChange(templateId) {
      // 根据选择的模板填充相关信息
      const template = this.templateOptions.find(item => item.templateId === templateId);
      console.log("选择的模板:", template);
      if (template) {
        // 设置基本信息
        this.form.week = template.templateWeek || template.week;
        this.form.planDescribe = template.templateDescribe;
        this.form.totalPlan = template.totalPlan;

        // 根据模板的星期自动设置计划日期
        this.setDateFromWeek(template.templateWeek || template.week);
        // 清空现有时间段列表
        this.form.planDetailList = [];

        // 如果模板有时间段信息，复制到排班计划中
        if (template.templateInfoList && template.templateInfoList.length > 0) {
          template.templateInfoList.forEach(templateInfo => {
            const timeSlot = {
              planDetailId: undefined,
              planId: undefined,
              stime: templateInfo.stime,
              etime: templateInfo.etime,
              ordinaryPlan: templateInfo.ordinaryPlan,
              emergencyPlan: templateInfo.emergencyPlan,
              reservePlan: templateInfo.reservePlan,
              planCount: templateInfo.planCount,
              isDel: 0
            };
            this.form.planDetailList.push(timeSlot);
          });
          this.calculateTotalTimeSlotPlan();
        } else {
          // 如果模板没有时间段信息，但有单个时间段的信息
          if (template.stime && template.etime) {
            const timeSlot = {
              planDetailId: undefined,
              planId: undefined,
              stime: template.stime,
              etime: template.etime,
              ordinaryPlan: template.ordinaryPlan || 0,
              emergencyPlan: template.emergencyPlan || 0,
              reservePlan: template.reservePlan || 0,
              planCount: template.planCount || 0,
              isDel: 0
            };
            this.form.planDetailList.push(timeSlot);
          } else {
            // 如果模板没有任何时间信息，添加一个空的时间段
            this.form.planDetailList.push(this.createEmptyTimeSlot());
          }
        }

        // 重新计算总号数
        this.calculateTotal();
      } else {
        // 如果没有选择模板，清空相关字段
        this.form.week = "";
        this.form.planDetailList = [this.createEmptyTimeSlot()];

        // 如果有计划日期，根据日期更新星期
        if (this.form.planDate) {
          this.updateWeekFromDate(this.form.planDate);
        }
      }
    },
    /** 日期变更事件 */
    handleDateChange(date) {
      if (date) {
        this.updateWeekFromDate(date);
      } else {
        this.form.week = "";
      }
    },
    /** 根据日期更新星期 */
    updateWeekFromDate(dateStr) {
      const date = new Date(dateStr);
      const weekDay = date.getDay();
      // 将JS的星期日(0)转换为星期日(7)，其他不变
      const week = weekDay === 0 ? "星期日" : `星期${["一","二","三","四","五","六"][weekDay - 1]}`;
      this.form.week = week;
    },
    /** 格式化星期显示 */
    formatWeek(week) {
      return week || "";
    },

    /** 星期变更事件 */
    handleWeekChange(week) {
      if (week) {
        // 根据星期设置日期
        this.setDateFromWeek(week);
      } else {
        this.form.planDate = "";
      }
    },

    /** 获取当前用户的部门ID并加载排班计划 */
    getCurrentUserDept() {
      // 使用getInfo API获取当前用户信息
      import('@/api/login').then(module => {
        module.getInfo().then(res => {
          if (res.code === 200 && res.user) {
            const user = res.user;
            console.log('当前用户信息:', user);

            // 假设用户信息中包含部门信息
            if (user.dept && user.dept.deptId) {
              const deptId = user.dept.deptId;
              console.log('当前用户部门ID:', deptId);

              // 设置查询参数
              this.queryParams.deptId = deptId;

              // 等待部门树加载完成
              this.waitForTreeAndSelectDept(deptId);
            } else {
              // 如果用户没有部门ID，则加载所有数据
              this.getList();
            }
          } else {
            // 获取用户信息失败，加载所有数据
            this.getList();
          }
        }).catch(() => {
          // 获取用户信息失败，加载所有数据
          this.getList();
        });
      });
    },

    /** 等待部门树加载完成并选中指定部门 */
    waitForTreeAndSelectDept(deptId) {
      // 如果树已经加载完成并且有数据
      if (this.$refs.tree && this.treeData && this.treeData.length > 0) {
        this.selectDeptNode(deptId);
      } else {
        // 如果树还没有加载完成，等待一段时间后再尝试
        setTimeout(() => {
          this.waitForTreeAndSelectDept(deptId);
        }, 100);
      }
    },

    /** 在部门树中选中指定部门 */
    selectDeptNode(deptId) {
      // 尝试在树中找到并选中当前用户的部门节点
      const findAndSelectNode = (nodes) => {
        if (!nodes || nodes.length === 0) return false;

        for (const node of nodes) {
          if (node.id === deptId) {
            // 找到并选中节点
            this.$refs.tree.setCurrentKey(node.id);
            this.handleNodeClick(node);
            return true;
          }

          // 递归检查子节点
          if (node.children && findAndSelectNode(node.children)) {
            return true;
          }
        }

        return false;
      };

      // 尝试选中节点
      const found = findAndSelectNode(this.treeData);

      // 如果没有找到节点，则直接加载数据
      if (!found) {
        this.getList();
      }
    },

    /** 根据星期设置日期 */
    setDateFromWeek(weekStr) {
      if (!weekStr) return;

      // 将星期转换为数字（0-6对应星期日-星期六）
      const weekMap = {
        '星期日': 0,
        '星期一': 1,
        '星期二': 2,
        '星期三': 3,
        '星期四': 4,
        '星期五': 5,
        '星期六': 6
      };

      const targetWeekDay = weekMap[weekStr];
      if (targetWeekDay === undefined) return;

      // 获取当前日期
      const today = new Date();
      const currentWeekDay = today.getDay(); // 0-6

      // 计算目标日期与当前日期的差距
      let daysToAdd = targetWeekDay - currentWeekDay;

      // 如果差距为负数，表示目标日期在下一周
      if (daysToAdd < 0) {
        daysToAdd += 7;
      }

      // 计算目标日期
      const targetDate = new Date(today);
      targetDate.setDate(today.getDate() + daysToAdd);

      // 格式化日期为 yyyy-MM-dd
      const year = targetDate.getFullYear();
      const month = String(targetDate.getMonth() + 1).padStart(2, '0');
      const day = String(targetDate.getDate()).padStart(2, '0');

      // 设置计划日期
      this.form.planDate = `${year}-${month}-${day}`;
    },

    /** 处理时间输入 */
    handleTimeInput(index, field, value) {
     
      if (this.editForm.planDetailList[index]) {
        this.$set(this.editForm.planDetailList[index], field, value);
        // 强制更新视图
        this.$forceUpdate();
      }
    }
  }
};
</script>

<style scoped>
/* 树节点样式 */
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 8px;
}
.custom-tree-node i {
  margin-right: 5px;
}
/* 设备图标颜色 */
.el-icon-monitor {
  color: #409EFF;
}
/* 部门图标颜色 */
.el-icon-office-building {
  color: #67C23A;
}
/* 设备列表样式 */
.device-list {
  list-style: none;
  padding: 0;
  margin: 0;
}
.device-list li {
  padding: 10px;
  cursor: pointer;
  border-bottom: 1px solid #eee;
  transition: all 0.3s;
}
.device-list li:hover {
  background-color: #f5f7fa;
}
.device-list li.active {
  background-color: #ecf5ff;
  color: #409EFF;
  font-weight: bold;
}
.device-list li i {
  margin-right: 5px;
}

/* 时间段样式 */
.time-slot-container {
  margin-bottom: 20px;
}

.time-slot-card {
  border: 1px solid #ebeef5;
  box-shadow: 0 2px 12px 0 rgba(0, 0, 0, 0.1);
}

.time-slot-total {
  margin-top: 10px;
  text-align: right;
  font-size: 14px;
  font-weight: bold;
}

.add-time-slot {
  margin: 20px 0;
  text-align: center;
}

.time-slot-item {
  margin-bottom: 5px;
  padding: 5px;
  border-bottom: 1px dashed #ebeef5;
}

.time-slot-item:last-child {
  border-bottom: none;
}

.plan-numbers {
  margin-top: 3px;
  font-size: 12px;
}

.ordinary-plan {
  color: #67C23A;
  font-weight: bold;
}

.emergency-plan {
  color: #F56C6C;
  font-weight: bold;
}

.reserve-plan {
  color: #409EFF;
  font-weight: bold;
}

/* 修改排班面板样式 */
.edit-info-box {
  display: flex;
  flex-wrap: wrap;
  background-color: #f5f7fa;
  border-radius: 4px;
  padding: 12px;
  margin-bottom: 15px;
  border-left: 3px solid #409EFF;
}

.edit-info-item {
  width: 50%;
  margin-bottom: 8px;
  display: flex;
  align-items: center;
}

.edit-info-label {
  font-weight: bold;
  color: #606266;
  margin-right: 5px;
  width: 60px;
  text-align: right;
}

.edit-info-value {
  color: #303133;
  flex: 1;
}

.edit-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10px;
}

.edit-total {
  display: flex;
  align-items: center;
  font-size: 14px;
  color: #606266;
}

.edit-total-number {
  font-size: 18px;
  font-weight: bold;
  color: #E6A23C;
  margin-left: 5px;
}

/* 时间段列表样式 */
.time-slot-list {
  margin-bottom: 15px;
}

.time-slot-item {
  border: 1px solid #ebeef5;
  border-radius: 4px;
  margin-bottom: 10px;
  padding: 10px;
  background-color: #fff;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
  position: relative;
}

.time-slot-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
  padding-bottom: 5px;
  border-bottom: 1px solid #ebeef5;
}

.time-slot-index {
  display: inline-block;
  text-align: center;
  font-size: 12px;
  font-weight: bold;
}

.delete-btn {
  padding: 2px 5px;
  color: #F56C6C;
}

.time-row {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 10px;
  padding: 5px 0;
  border-bottom: 1px dashed #ebeef5;
}

.number-row {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 5px 0;
}

.number-item {
  display: flex;
  align-items: center;
  margin: 0 5px;
}

.number-label {
  font-size: 12px;
  color: #606266;
  margin-right: 5px;
  white-space: nowrap;
}
</style>
