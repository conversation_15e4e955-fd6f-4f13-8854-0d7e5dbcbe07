import request from '@/utils/request'

// 获取首页统计数据
export function getIndexStatistics() {
  return request({
    url: '/index/statistics',
    method: 'get'
  })
}

// 获取设备工作量统计
export function getDeviceWorkload() {
  return request({
    url: '/index/device/workload',
    method: 'get'
  })
}

// 获取预约趋势数据
export function getAppointmentTrend(params) {
  return request({
    url: '/index/appointment/trend',
    method: 'get',
    params
  })
}

// 获取预约类型分布
export function getAppointmentTypeDistribution() {
  return request({
    url: '/index/appointment/type',
    method: 'get'
  })
}
